<script lang="ts" setup>
import type { UploadFile } from 'ant-design-vue';

import type {
  AvatarListItem,
  CloneSetConfig,
  VoiceTrainListItem,
  WayInfo,
} from '#/api/core/digital-human';
import type {
  VideoMaterialItem,
  VideoMaterialListParams,
} from '#/api/core/video-matrix';

import { computed, h, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Checkbox,
  Drawer,
  message,
  Modal,
  Pagination,
  Radio,
} from 'ant-design-vue';

import {
  avatarClone,
  deleteAvatar,
  deleteVideo,
  deleteVoice,
  getAvatarList,
  getCloneSetConfig,
  getVideoList,
  getVoiceTrainList,
  getWayList,
  initOSSConfig,
  isOSSReady,
  uploadAvatar,
  uploadDigitalHumanVideo,
} from '#/api/core/digital-human';
import {
  deleteUserCollect,
  getAIRewriteUrl,
  getUserCollectList,
  updateUserCollect,
} from '#/api/core/user-collect';
import { deleteMaterial, getVideoMaterialList } from '#/api/core/video-matrix';
import { createStreamHandler } from '#/utils/stream-request';

import ProductVideoListModal from './components/ProductVideoListModal.vue';

defineOptions({ name: 'AIDigitalHuman' });

// 初始化路由
const router = useRouter();

// 菜单数据结构
interface MenuItem {
  id: number;
  name: string;
  children?: MenuItem[];
}

// 菜单数据
const menuTabs = ref<MenuItem[]>([
  {
    id: 1,
    name: '我的形象',
    children: [], // 初始化为空数组，根据接口数据动态添加
  },
  {
    id: 2,
    name: '我的音色',
    children: [
      { id: 1, name: '入门版' },
      { id: 3, name: '专业版' },
      { id: 2, name: '高保真' },
    ],
  },
  {
    id: 3,
    name: '我的作品',
    children: [], // 初始化为空数组，根据接口数据动态添加
  },
  {
    id: 4,
    name: '成品库',
    // 成品库没有子菜单
  },
  {
    id: 5,
    name: 'AI收藏',
    children: [
      { id: 1, name: '文本' },
      { id: 2, name: '图片' },
      { id: 3, name: '视频' },
    ],
  },
]);

// 当前选中的菜单状态
const activeTabId = ref(1); // 默认选中"我的形象"
const activeSubTabId = ref(0); // 初始为0，等待线路数据加载后设置

// 当前选中的一级菜单
const currentTab = computed(() => {
  return menuTabs.value.find((tab) => tab.id === activeTabId.value);
});

// 当前二级菜单选项
const currentSubTabs = computed(() => {
  return currentTab.value?.children || [];
});

// 分页状态
const currentPage = ref(1);
const pageSize = 9;

// 根据菜单类型生成对应的数据
const generateDataByMenuType = (tabId: number, subTabId: number) => {
  const count = 12; // 每个分类的数据数量
  let menuType = '';
  let type = '';

  // 根据一级菜单确定类型
  switch (tabId) {
    case 1: {
      // 我的形象
      menuType = 'image-type';
      type = 'image';
      break;
    }
    case 2: {
      // 我的音色
      menuType = 'audio-type';
      type = 'audio';
      break;
    }
    case 3: {
      // 我的作品
      menuType = 'work-type';
      type = 'work';
      break;
    }
    case 4: {
      // 成品库
      menuType = 'library-type';
      type = 'library';
      break;
    }
    default: {
      menuType = 'image-type';
      type = 'image';
    }
  }

  return Array.from({ length: count }, (_, i) => ({
    id: `${tabId}-${subTabId}-${i + 1}`,
    title: `内容${i + 1}`,
    thumbnail: '',
    date: '2025-01-16',
    type,
    menuType,
  }));
};

// 处理一级菜单切换
const handleTabChange = (tabId: number) => {
  activeTabId.value = tabId;
  // 切换一级菜单时，自动选择第一个二级菜单
  const selectedTab = menuTabs.value.find((tab) => tab.id === tabId);
  activeSubTabId.value =
    selectedTab?.children && selectedTab.children.length > 0
      ? selectedTab.children[0]!.id
      : 0; // 没有子菜单时设为0
  currentPage.value = 1; // 重置到第一页
};

// 处理二级菜单切换
const handleSubTabChange = (subTabId: number) => {
  activeSubTabId.value = subTabId;
  currentPage.value = 1; // 重置到第一页
};

// 数字人克隆弹窗状态
const drawerVisible = ref(false);

// 视频要求提示弹窗状态
const videoRequirementVisible = ref(false);

// OSS配置加载状态
const ossConfigLoading = ref(false);

// 克隆设置配置
const cloneSetConfig = ref<CloneSetConfig | null>(null);
const cloneSetLoading = ref(false);

// 线路数据
const wayList = ref<WayInfo[]>([]);
const wayListLoading = ref(false);

// 统一的列表数据
const listData = ref<any[]>([]);
const listLoading = ref(false);
const listPagination = ref({
  current: 1,
  pageSize: 6,
  total: 0,
  totalPage: 0,
});

// 视频播放弹窗
const videoPlayerVisible = ref(false);
const currentVideoUrl = ref('');

// 成品视频列表弹窗
const productVideoModalVisible = ref(false);
const selectedProductId = ref(0);
const selectedProductTitle = ref('');

// AI收藏文本详情弹窗
const textDetailModalVisible = ref(false);
const currentTextItem = ref<any>(null);
const editedTextContent = ref('');
const aiRewriteLoading = ref(false);

// 表单数据
const formData = ref({
  trainingMode: 1, // 1: 标清, 2: 高清
  videoFile: null as null | UploadFile,
  agreeToTerms: false,
});

// 上传状态
const uploading = ref(false);
const uploadProgress = ref(0);

// 处理卡片点击事件
const handleCardClick = (cardId: number) => {
  console.warn(`点击了ID为${cardId}的板块`);

  switch (cardId) {
    case 1: {
      // 形象克隆
      drawerVisible.value = true;
      // 自动获取阿里云OSS配置
      initAliyunConfig();

      break;
    }
    case 2: {
      // 声音克隆
      router.push('/public-domain/voice-clone');

      break;
    }
    case 3: {
      // 视频创作
      router.push('/public-domain/video-creation');

      break;
    }
    // No default
  }
};

// 关闭弹窗
const closeDrawer = () => {
  drawerVisible.value = false;
  // 重置表单数据
  formData.value = {
    trainingMode: 1,
    videoFile: null,
    agreeToTerms: false,
  };
  uploading.value = false;
  uploadProgress.value = 0;
};

// 视频上传处理 - 使用统一的OSS上传器
const handleVideoUpload = async (file: File) => {
  if (!isOSSReady()) {
    message.error('上传配置未就绪，请稍后重试');
    return;
  }

  try {
    uploading.value = true;
    uploadProgress.value = 0;

    console.warn('开始上传视频:', file.name);

    // 使用统一的OSS上传器
    const result = await uploadDigitalHumanVideo(file, (progress) => {
      uploadProgress.value = progress;
      console.warn(`上传进度: ${progress}%`);
    });

    // 上传成功
    uploading.value = false;
    uploadProgress.value = 100;

    // 保存文件信息和视频URL
    formData.value.videoFile = {
      ...file,
      url: result.url,
      name: result.fileName,
      size: result.size,
      type: result.type,
      originFileObj: file,
    } as any;

    console.warn('视频上传成功，文件URL:', result.url);
    message.success('视频上传成功');
  } catch (error) {
    console.error('视频上传失败:', error);
    uploading.value = false;
    uploadProgress.value = 0;
    const errorMessage = error instanceof Error ? error.message : '请重试';
    message.error(`视频上传失败: ${errorMessage}`);
  }
};

// 视频上传前的验证
const beforeVideoUpload = (file: File) => {
  const isVideo = file.type.startsWith('video/');
  if (!isVideo) {
    message.error('只能上传视频文件！');
    return false;
  }

  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error('视频文件大小不能超过100MB！');
    return false;
  }

  return true;
};

// 移除视频
const handleVideoRemove = () => {
  formData.value.videoFile = null;
  uploadProgress.value = 0;
};

// 表单验证
const canSubmit = computed(() => {
  return (
    formData.value.videoFile && formData.value.agreeToTerms && !uploading.value
  );
});

// 视频预览URL
const videoPreviewUrl = computed(() => {
  if (!formData.value.videoFile) return '';
  if (formData.value.videoFile.url) return formData.value.videoFile.url;
  if (formData.value.videoFile.originFileObj) {
    return window.URL.createObjectURL(formData.value.videoFile.originFileObj);
  }
  return '';
});

// 开始上传
const handleSubmit = async () => {
  if (!canSubmit.value) return;

  // 检查是否有视频文件和视频URL
  if (!formData.value.videoFile || !formData.value.videoFile.url) {
    message.error('请先上传视频文件');
    return;
  }

  try {
    // 显示加载状态
    uploading.value = true;

    console.warn('开始上传数字人克隆任务:', {
      trainingMode: formData.value.trainingMode,
      videoFile: formData.value.videoFile,
      agreeToTerms: formData.value.agreeToTerms,
    });

    // 调用uploadAvatar接口
    const params = {
      video_url: formData.value.videoFile.url,
      definition: formData.value.trainingMode, // 1: 标清, 2: 高清
    };

    console.warn('调用uploadAvatar接口，参数:', params);

    const result = await uploadAvatar(params);

    console.warn('uploadAvatar接口响应:', result);

    message.success('数字人克隆任务已提交！');
    closeDrawer();
  } catch (error) {
    console.error('提交数字人克隆任务失败:', error);
    const errorMessage =
      error instanceof Error ? error.message : '提交失败，请重试';
    message.error(`提交失败: ${errorMessage}`);
  } finally {
    uploading.value = false;
  }
};

// 查看用户协议
const viewUserAgreement = () => {
  if (cloneSetConfig.value?.protocol) {
    // 显示协议内容弹窗
    Modal.info({
      title: cloneSetConfig.value.protocol_name || '用户协议',
      content: h('div', {
        innerHTML: cloneSetConfig.value.protocol,
      }),
      width: 600,
      okText: '我知道了',
    });
  } else {
    message.warning('协议内容加载中，请稍后重试');
  }
};

// 获取形象状态显示文字
const getAvatarStatusText = (item: AvatarListItem, routeId: number): string => {
  // 首先判断is_status
  if (item.is_status === 1) return '审核中';
  if (item.is_status === 3) return '已驳回';

  // is_status === 2时，根据线路判断具体状态
  if (item.is_status === 2) {
    let statusField = '';

    // 根据线路ID选择对应的状态字段
    switch (routeId) {
      case 1: {
        statusField = item.current_status;
        break;
      }
      case 2: {
        statusField = item.new_current_status;
        break;
      }
      case 3: {
        statusField = item.composite_current_status;
        break;
      }
      case 4: {
        statusField = item.four_current_status;
        break;
      }
      default: {
        statusField = item.current_status;
      }
    }

    // 根据状态值返回对应文字
    switch (statusField) {
      case 'completed': {
        return '已完成';
      }
      case 'failed': {
        return '已失败';
      }
      case 'nothing': {
        return '未训练';
      }
      default: {
        return '克隆中';
      }
    }
  }

  return '未知状态';
};

// 判断是否为未训练状态
const isUntrainedStatus = (item: AvatarListItem, routeId: number): boolean => {
  if (item.is_status !== 2) return false;

  let statusField = '';
  switch (routeId) {
    case 1: {
      statusField = item.current_status;
      break;
    }
    case 2: {
      statusField = item.new_current_status;
      break;
    }
    case 3: {
      statusField = item.composite_current_status;
      break;
    }
    case 4: {
      statusField = item.four_current_status;
      break;
    }
    default: {
      statusField = item.current_status;
    }
  }

  return statusField === 'nothing';
};

// 判断是否为已完成状态
const isCompletedStatus = (item: AvatarListItem, routeId: number): boolean => {
  if (item.is_status !== 2) return false;

  let statusField = '';
  switch (routeId) {
    case 1: {
      statusField = item.current_status;
      break;
    }
    case 2: {
      statusField = item.new_current_status;
      break;
    }
    case 3: {
      statusField = item.composite_current_status;
      break;
    }
    case 4: {
      statusField = item.four_current_status;
      break;
    }
    default: {
      statusField = item.current_status;
    }
  }

  return statusField === 'completed';
};

// 打开视频播放弹窗
const openVideoPlayer = (videoUrl: string) => {
  currentVideoUrl.value = videoUrl;
  videoPlayerVisible.value = true;
};

// 关闭视频播放弹窗
const closeVideoPlayer = () => {
  videoPlayerVisible.value = false;
  currentVideoUrl.value = '';
};

// 处理项目点击
const handleItemClick = (item: any) => {
  const currentTabName = currentTab.value?.name || '';
  const currentSubTabName =
    currentSubTabs.value.find((sub) => sub.id === activeSubTabId.value)?.name ||
    '';

  // 判断是否是"我的作品"且可以播放
  if (
    activeTabId.value === 3 &&
    item.current_status === 'success' &&
    item.result
  ) {
    openVideoPlayer(item.result);
    return;
  }

  // 判断是否是成品库，只有完成状态才能打开成品视频列表弹窗
  if (activeTabId.value === 4) {
    if (item.status === 4) {
      openProductVideoModal(item);
    } else {
      message.info('只有完成状态的成品才能查看视频列表');
    }
    return;
  }

  // 判断是否是AI收藏
  if (activeTabId.value === 5) {
    // 文本类型打开详情弹窗
    if (activeSubTabId.value === 1) {
      openTextDetailModal(item);
      return;
    }

    // 图片类型的处理
    if (activeSubTabId.value === 2) {
      // 如果有内容链接，可以预览图片
      if (item.content) {
        console.warn(`预览${currentSubTabName}:`, item.content);
        // 这里可以添加图片预览逻辑
      }
      return;
    }

    // 视频类型的处理
    if (activeSubTabId.value === 3) {
      // 视频已经通过video组件直接播放，点击事件可以用于其他操作
      if (item.content) {
        console.warn(`视频收藏点击:`, item.content);
        // 可以添加全屏播放或其他操作
      }
      return;
    }
  }

  console.warn(
    `点击了${currentTabName} > ${currentSubTabName} > ${item.title || item.name}`,
  );
};

// 打开成品视频列表弹窗
const openProductVideoModal = (product: VideoMaterialItem) => {
  selectedProductId.value = product.id;
  selectedProductTitle.value = product.title;
  productVideoModalVisible.value = true;
};

// 处理删除操作
const handleDelete = async (item: any, event: Event) => {
  event.stopPropagation(); // 阻止事件冒泡

  const currentTabName = currentTab.value?.name || '';
  const currentSubTabName =
    currentSubTabs.value.find((sub) => sub.id === activeSubTabId.value)?.name ||
    '';

  // 二次确认
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${item.title || item.type_tile || '该项目'}"吗？\n位置：${currentTabName} > ${currentSubTabName}\n此操作不可撤销。`,
    onOk: async () => {
      try {
        // 根据不同类型调用对应的删除接口
        switch (activeTabId.value) {
          case 1: {
            // 我的形象
            await deleteAvatar({ avatar_id: item.id });
            break;
          }
          case 2: {
            // 我的音色
            await deleteVoice({ voice_id: item.id });
            break;
          }
          case 3: {
            // 我的作品
            await deleteVideo({ task_id: item.id, video_id: item.id });
            break;
          }
          case 4: {
            // 成品库
            await deleteMaterial({ id: item.id });
            break;
          }
          case 5: {
            // AI收藏
            await deleteUserCollect({ id: item.id });
            break;
          }
          default: {
            console.warn('未知的删除类型');
            return;
          }
        }

        message.success('删除成功');
        // 刷新列表
        await loadListData(listPagination.value.current);

        console.warn(
          `删除了${currentTabName} > ${currentSubTabName} > ${item.title || item.type_tile || item.name}`,
        );
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败，请重试');
      }
    },
  });
};

// 获取音色状态显示文字
const getVoiceStatusText = (item: VoiceTrainListItem): string => {
  // is_status === 2 时，根据current_status判断
  if (item.is_status === 2) {
    if (item.current_status === 'completed') {
      return '已完成';
    } else if (item.current_status === 'failed') {
      return '已失败';
    } else {
      return '未完成';
    }
  }
  // 其他情况
  return item.is_status === 3 ? '已驳回' : '审核中';
};

// 统一加载列表数据
const loadListData = async (page: number = 1) => {
  listLoading.value = true;
  try {
    // 根据当前选中的菜单类型加载不同的数据
    switch (activeTabId.value) {
      case 1: {
        // 我的形象
        const definition = activeSubTabId.value === 4 ? 2 : 1;
        const params = {
          definition,
          page,
          psize: listPagination.value.pageSize,
        };

        console.warn('获取我的形象列表，参数:', params);
        const response = await getAvatarList(params);
        listData.value = response.list;
        listPagination.value = {
          current: response.pindex,
          pageSize: response.psize,
          total: response.total,
          totalPage: response.totalPage,
        };
        console.warn('我的形象列表获取成功:', response);

        break;
      }
      case 2: {
        // 我的音色
        const params = {
          name: '',
          page,
          psize: listPagination.value.pageSize,
          train_mode: activeSubTabId.value,
        };

        console.warn('获取我的音色列表，参数:', params);
        const response = await getVoiceTrainList(params);
        listData.value = response.list;
        listPagination.value = {
          current: response.pindex,
          pageSize: response.psize,
          total: response.total,
          totalPage: response.totalPage,
        };
        console.warn('我的音色列表获取成功:', response);

        break;
      }
      case 3: {
        // 我的作品
        // 映射线路ID到type参数
        let type = 1; // 默认值
        switch (activeSubTabId.value) {
          case 1: {
            type = 1; // 线路一
            break;
          }
          case 2: {
            type = 3; // 线路二
            break;
          }
          case 3: {
            type = 2; // 线路三
            break;
          }
          case 4: {
            type = 4; // 线路四
            break;
          }
        }

        const params = {
          name: '',
          page,
          psize: listPagination.value.pageSize,
          type,
        };

        console.warn('获取我的作品列表，参数:', params);
        const response = await getVideoList(params);
        listData.value = response.list;
        listPagination.value = {
          current: response.pindex,
          pageSize: response.psize,
          total: response.total,
          totalPage: response.totalPage,
        };
        console.warn('我的作品列表获取成功:', response);

        break;
      }
      case 4: {
        // 成品库
        const params: VideoMaterialListParams = {
          page,
          psize: listPagination.value.pageSize,
        };

        console.warn('获取成品库列表，参数:', params);
        const response = await getVideoMaterialList(params);
        listData.value = response.list;
        listPagination.value = {
          current: response.pindex,
          pageSize: response.psize,
          total: response.total,
          totalPage: response.totalPage,
        };
        console.warn('成品库列表获取成功:', response);

        break;
      }
      case 5: {
        // AI收藏
        const params = {
          category: activeSubTabId.value, // 1=文本，2=图片，3=视频
          page,
        };

        console.warn('获取AI收藏列表，参数:', params);
        const response = await getUserCollectList(params);
        listData.value = response.list;
        listPagination.value = {
          current: response.pindex,
          pageSize: response.psize,
          total: response.total,
          totalPage: response.totalPage,
        };
        console.warn('AI收藏列表获取成功:', response);

        break;
      }
      default: {
        // 其他菜单
        // 这里使用默认数据或其他API
        listData.value = generateDataByMenuType(
          activeTabId.value,
          activeSubTabId.value,
        );
        listPagination.value = {
          current: page,
          pageSize,
          total: listData.value.length,
          totalPage: Math.ceil(listData.value.length / pageSize),
        };
      }
    }
  } catch (error) {
    console.error('获取列表数据失败:', error);
    message.error('获取数据失败，请重试');
    listData.value = [];
    listPagination.value.total = 0;
    listPagination.value.totalPage = 0;
  } finally {
    listLoading.value = false;
  }
};

// 页面初始化
onMounted(async () => {
  // 并行获取配置数据
  await Promise.all([
    initCloneSetConfig(), // 获取克隆设置配置
    initWayList(), // 获取线路数据
  ]);
});

// 显示视频要求提示弹窗
const showVideoRequirement = () => {
  videoRequirementVisible.value = true;
};

// 关闭视频要求提示弹窗
const closeVideoRequirement = () => {
  videoRequirementVisible.value = false;
};

// 获取线路数据
const initWayList = async () => {
  try {
    wayListLoading.value = true;
    wayList.value = await getWayList();
    console.warn('线路数据获取成功:', wayList.value);

    // 根据线路数据动态更新菜单
    updateMenuWithWayData();
  } catch (error) {
    console.error('获取线路数据失败:', error);
    message.error('获取线路数据失败，请重试');
  } finally {
    wayListLoading.value = false;
  }
};

// 是否显示克隆模式，存在线路四就显示
const showTrainingMode = computed(() => {
  return wayList.value.some((way) => way.id === 4);
});

// 根据线路数据更新菜单
const updateMenuWithWayData = () => {
  if (wayList.value.length === 0) return;

  // 按sort字段排序线路数据
  const sortedWayList = [...wayList.value].sort((a, b) => a.sort - b.sort);

  // 更新"我的形象"的children
  const myImageTab = menuTabs.value.find((tab) => tab.id === 1);
  if (myImageTab) {
    myImageTab.children = sortedWayList.map((way) => ({
      id: way.id,
      name: way.name,
    }));
  }

  // 更新"我的作品"的children
  const myWorksTab = menuTabs.value.find((tab) => tab.id === 3);
  if (myWorksTab) {
    myWorksTab.children = sortedWayList.map((way) => ({
      id: way.id, // 直接使用原始ID
      name: way.name,
    }));
  }

  // 设置默认选中的子菜单（选中第一个线路）
  if (activeSubTabId.value === 0 && sortedWayList.length > 0) {
    const firstWay = sortedWayList[0];
    if (firstWay) {
      activeSubTabId.value = firstWay.id;
    }
  }

  console.warn('菜单数据更新完成:', menuTabs.value);
};

// 监听选中的子菜单变化，加载对应的列表数据
watch(
  activeSubTabId,
  (newSubTabId) => {
    // 重置分页到第1页
    if (newSubTabId > 0) {
      loadListData(1);
    }
  },
  { immediate: false },
);

// 监听一级菜单变化
watch(
  activeTabId,
  (newTabId) => {
    // 成品库没有子菜单，直接加载数据
    if (newTabId === 4) {
      currentPage.value = 1;
      loadListData(1);
    } else if (newTabId === 5) {
      // AI收藏有子菜单，需要等待子菜单选择
      if (activeSubTabId.value > 0) {
        currentPage.value = 1;
        loadListData(1);
      }
    } else if (activeSubTabId.value > 0) {
      // 其他有子菜单的情况
      currentPage.value = 1;
      loadListData(1);
    }
  },
  { immediate: false },
);

// 获取克隆设置配置
const initCloneSetConfig = async () => {
  try {
    cloneSetLoading.value = true;
    cloneSetConfig.value = await getCloneSetConfig();
    console.warn('克隆设置配置获取成功:', cloneSetConfig.value);
  } catch (error) {
    console.error('获取克隆设置配置失败:', error);
    message.error('获取配置失败，请重试');
  } finally {
    cloneSetLoading.value = false;
  }
};

// 初始化阿里云OSS配置
const initAliyunConfig = async () => {
  try {
    ossConfigLoading.value = true;
    await initOSSConfig();
    console.warn('阿里云OSS配置初始化成功');
  } catch (error) {
    console.error('获取阿里云OSS配置失败:', error);
    message.error('获取上传配置失败，请重试');
  } finally {
    ossConfigLoading.value = false;
  }
};

// 从视频要求弹窗开始上传
const startVideoUpload = () => {
  // 检查阿里云配置是否已加载
  if (!isOSSReady()) {
    message.error('请配置阿里云');
    return;
  }

  videoRequirementVisible.value = false;
  // 触发文件选择
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'video/*';
  input.addEventListener('change', (e) => {
    const file = (e.target as HTMLInputElement).files?.[0];
    if (file && beforeVideoUpload(file)) {
      handleVideoUpload(file);
    }
  });
  input.click();
};

// 处理形象卡片点击
const handleAvatarCardClick = async (item: AvatarListItem) => {
  const routeId = activeSubTabId.value;

  if (isUntrainedStatus(item, routeId)) {
    try {
      // 未训练状态，调用克隆API
      const params = {
        way: routeId + 1, // 线路ID+1
        id: item.id, // 头像ID
      };

      message.loading('正在提交克隆请求...', 1);
      await avatarClone(params);
      message.success('克隆请求已提交成功');

      // 成功后刷新当前列表
      await loadListData(listPagination.value.current);
    } catch (error) {
      console.error('头像克隆失败:', error);
      message.error('克隆请求失败，请重试');
    }
  } else if (isCompletedStatus(item, routeId) && item.video_url) {
    // 已完成状态，播放视频
    openVideoPlayer(item.video_url);
  }
};

// 获取作品状态显示文字
const getVideoStatusText = (item: any): string => {
  // 根据current_status判断状态
  switch (item.current_status) {
    case 'fail': {
      return '已失效';
    }
    case 'success': {
      return '已完成';
    }
    case 'wait_sent': {
      return '等待中';
    }
    default: {
      return '生成中';
    }
  }
};

// 获取成品库状态显示文字
const getLibraryStatusText = (item: VideoMaterialItem): string => {
  // 根据status判断状态：<4合成中 4完成 5失败 6部分成功
  if (item.status < 4) {
    return '合成中';
  } else
    switch (item.status) {
      case 4: {
        return '完成';
      }
      case 5: {
        return '失败';
      }
      case 6: {
        return '部分成功';
      }
      // No default
    }
  return '未知状态';
};

// 打开文本详情弹窗
const openTextDetailModal = (item: any) => {
  currentTextItem.value = item;
  editedTextContent.value = item.content;
  textDetailModalVisible.value = true;
};

// 关闭文本详情弹窗
const closeTextDetailModal = () => {
  textDetailModalVisible.value = false;
  currentTextItem.value = null;
  editedTextContent.value = '';
  aiRewriteLoading.value = false;
};

// AI改写功能
const handleAIRewrite = async () => {
  if (!editedTextContent.value.trim()) {
    message.warning('请输入要改写的内容');
    return;
  }

  try {
    aiRewriteLoading.value = true;

    // 清空当前内容
    editedTextContent.value = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: getAIRewriteUrl(),
      method: 'POST',
      body: {
        content: currentTextItem.value.content,
        type: 3, // 文案改写
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            editedTextContent.value += jsonData.data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到内容中
          editedTextContent.value += data;
        }
      },
      onComplete: () => {
        message.success('AI改写完成');
        aiRewriteLoading.value = false;
      },
      onError: (error: Error) => {
        console.error('AI改写失败:', error);
        message.error(`AI改写失败: ${error.message}`);
        aiRewriteLoading.value = false;
      },
    });
  } catch (error) {
    console.error('AI改写失败:', error);
    message.error('AI改写失败，请重试');
    aiRewriteLoading.value = false;
  }
};

// 复制文案
const handleCopyText = async () => {
  if (!editedTextContent.value.trim()) {
    message.warning('没有可复制的内容');
    return;
  }

  try {
    await navigator.clipboard.writeText(editedTextContent.value);
    message.success('复制成功');
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败，请手动复制');
  }
};

// 保存文案
const handleSaveText = async () => {
  if (!editedTextContent.value.trim()) {
    message.warning('请输入要保存的内容');
    return;
  }

  if (!currentTextItem.value?.id) {
    message.error('缺少必要的保存信息');
    return;
  }

  try {
    const params = {
      content: editedTextContent.value,
      id: currentTextItem.value.id,
    };

    await updateUserCollect(params);
    message.success('保存成功');

    // 更新当前项的内容
    currentTextItem.value.content = editedTextContent.value;

    // 刷新列表
    await loadListData(listPagination.value.current);

    closeTextDetailModal();
  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请重试');
  }
};

// 视频加载错误处理
const handleVideoError = (event: Event) => {
  console.error('视频加载失败:', event);
  // 可以在这里添加错误处理逻辑，比如显示默认图片
};
</script>

<template>
  <Page auto-content-height>
    <div class="ai-digital-human-container">
      <!-- 主要功能卡片容器 -->
      <div class="cards-container">
        <!-- 数字人克隆卡片 -->
        <div
          class="service-card digital-human-card"
          @click="handleCardClick(1)"
        >
          <div class="card-content">
            <div class="card-icon">
              <div class="icon-wrapper digital-human-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 10.1 14.1 11 13 11S11 10.1 11 9V7.5L5 7V9C5 10.1 4.1 11 3 11S1 10.1 1 9V7C1 6.4 1.4 6 2 6L10 6.5C10 5.1 10.9 4 12 4S14 5.1 14 6.5L22 6C22.6 6 23 6.4 23 7V9C23 10.1 22.1 11 21 11S19 10.1 19 9Z"
                    fill="currentColor"
                  />
                  <path
                    d="M12 12C8.7 12 6 14.7 6 18V22H18V18C18 14.7 15.3 12 12 12Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div class="card-text">
              <h3 class="card-title">形象克隆</h3>
              <p class="card-subtitle">一分钟生成形象</p>
            </div>
          </div>
        </div>

        <!-- 声音克隆卡片 -->
        <div class="service-card voice-clone-card" @click="handleCardClick(2)">
          <div class="card-content">
            <div class="card-icon">
              <div class="icon-wrapper voice-clone-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2C13.1 2 14 2.9 14 4V12C14 13.1 13.1 14 12 14C10.9 14 10 13.1 10 12V4C10 2.9 10.9 2 12 2Z"
                    fill="currentColor"
                  />
                  <path
                    d="M19 10V12C19 15.9 15.9 19 12 19C8.1 19 5 15.9 5 12V10H7V12C7 14.8 9.2 17 12 17C14.8 17 17 14.8 17 12V10H19Z"
                    fill="currentColor"
                  />
                  <path d="M11 21V23H13V21H11Z" fill="currentColor" />
                  <path d="M9 22H15V24H9V22Z" fill="currentColor" />
                </svg>
              </div>
            </div>
            <div class="card-text">
              <h3 class="card-title">声音克隆</h3>
              <p class="card-subtitle">仅需30秒声音复刻</p>
            </div>
          </div>
        </div>

        <!-- 视频创作卡片 -->
        <div
          class="service-card video-creation-card"
          @click="handleCardClick(3)"
        >
          <div class="card-content">
            <div class="card-icon">
              <div class="icon-wrapper video-creation-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17 10.5V7C17 6.45 16.55 6 16 6H4C3.45 6 3 6.45 3 7V17C3 17.55 3.45 18 4 18H16C16.55 18 17 17.55 17 17V13.5L21 17.5V6.5L17 10.5Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div class="card-text">
              <h3 class="card-title">视频创作</h3>
              <p class="card-subtitle">生成数字人视频</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 一级菜单导航 -->
      <div class="tabs-container">
        <div class="tabs-wrapper">
          <div
            v-for="tab in menuTabs"
            :key="tab.id"
            class="tab-item"
            :class="{ active: activeTabId === tab.id }"
            @click="handleTabChange(tab.id)"
          >
            {{ tab.name }}
          </div>
        </div>
      </div>

      <!-- 二级菜单导航 -->
      <div v-if="currentSubTabs.length > 0" class="sub-tabs-container">
        <div class="sub-tabs-wrapper">
          <div
            v-for="subTab in currentSubTabs"
            :key="subTab.id"
            class="sub-tab-item"
            :class="{ active: activeSubTabId === subTab.id }"
            @click="handleSubTabChange(subTab.id)"
          >
            {{ subTab.name }}
          </div>
        </div>
        <!-- 我的作品提示信息 -->
        <div
          v-if="activeTabId === 3 && cloneSetConfig?.video_expire"
          class="video-expire-notice"
        >
          注意: 视频生成成功后只保留{{ cloneSetConfig.video_expire }}天,
          过期后自动删除
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-container">
        <!-- 统一列表区域 -->
        <div class="content-grid">
          <!-- 加载中提示 -->
          <div v-if="listLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <span class="loading-text">正在加载数据...</span>
          </div>

          <!-- 无数据提示 -->
          <div v-else-if="listData.length === 0" class="empty-container">
            <div class="empty-text">暂无数据</div>
          </div>

          <!-- 渲染列表内容 -->
          <template v-else>
            <!-- 我的形象列表项 -->
            <div
              v-for="item in listData"
              :key="item.id"
              class="content-item"
              :class="{
                'avatar-item': activeTabId === 1,
                'voice-item': activeTabId === 2,
                'video-item': activeTabId === 3,
                'collect-item': activeTabId === 5,
                [item.menuType]:
                  activeTabId !== 1 &&
                  activeTabId !== 2 &&
                  activeTabId !== 3 &&
                  activeTabId !== 5,
              }"
              @click="
                activeTabId === 1
                  ? handleAvatarCardClick(item)
                  : handleItemClick(item)
              "
            >
              <div class="item-thumbnail">
                <!-- 形象列表封面 -->
                <img
                  v-if="activeTabId === 1 && item.video_cover"
                  :src="item.video_cover"
                  alt="封面"
                  class="avatar-cover"
                />

                <!-- 音色列表封面 -->
                <img
                  v-else-if="activeTabId === 2"
                  src="https://szr.jiajs.cn/index/246.png"
                  alt="音色"
                  class="voice-cover"
                />

                <!-- 作品列表封面 -->
                <img
                  v-else-if="activeTabId === 3"
                  :src="
                    item.result_cover
                      ? item.result_cover
                      : item.current_status === 'success'
                        ? 'https://szr.jiajs.cn/index/134.png'
                        : item.current_status === 'fail'
                          ? 'https://szr.jiajs.cn/index/132.png'
                          : 'https://szr.jiajs.cn/index/131.png'
                  "
                  alt="作品封面"
                  class="video-cover"
                />

                <!-- 成品库列表封面 -->
                <img
                  v-else-if="activeTabId === 4"
                  src="https://szr.jiajs.cn/index/306.png"
                  alt="成品库"
                  class="library-cover"
                />

                <!-- AI收藏列表封面 -->
                <!-- 文本类型显示内容预览 -->
                <div
                  v-if="activeTabId === 5 && activeSubTabId === 1"
                  class="collect-text-content"
                >
                  {{ item.content || '暂无内容' }}
                </div>
                <img
                  v-else-if="activeTabId === 5 && activeSubTabId === 2"
                  :src="item.content || 'https://szr.jiajs.cn/index/134.png'"
                  alt="图片收藏"
                  class="collect-image-cover"
                />
                <video
                  v-else-if="activeTabId === 5 && activeSubTabId === 3"
                  :src="item.content"
                  class="collect-video-player"
                  controls
                  preload="metadata"
                  @error="handleVideoError"
                >
                  您的浏览器不支持视频播放
                </video>

                <!-- 默认封面 -->
                <div v-else class="thumbnail-placeholder">
                  <!-- 根据类型显示不同的图标 -->
                  <svg
                    v-if="activeTabId === 2 || (item && item.type === 'audio')"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M3 9V15H7L12 20V4L7 9H3ZM16.5 12C16.5 10.23 15.48 8.71 14 7.97V16.02C15.48 15.29 16.5 13.77 16.5 12ZM14 3.23V5.29C16.89 6.15 19 8.83 19 12C19 15.17 16.89 17.85 14 18.71V20.77C18.01 19.86 21 16.28 21 12C21 7.72 18.01 4.14 14 3.23Z"
                      fill="currentColor"
                    />
                  </svg>
                  <svg
                    v-else-if="item && item.type === 'library'"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4 6H2V20C2 21.1 2.9 22 4 22H18V20H4V6ZM20 2H8C6.9 2 6 2.9 6 4V16C6 17.1 6.9 18 8 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H8V4H20V16Z"
                      fill="currentColor"
                    />
                  </svg>
                  <svg
                    v-else
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>

                <!-- 状态显示 - 形象列表 -->
                <div
                  v-if="
                    activeTabId === 1 &&
                    !isUntrainedStatus(item, activeSubTabId)
                  "
                  class="avatar-status"
                >
                  {{ getAvatarStatusText(item, activeSubTabId) }}
                </div>

                <!-- 未训练状态显示 - 形象列表 -->
                <div
                  v-if="
                    activeTabId === 1 && isUntrainedStatus(item, activeSubTabId)
                  "
                  class="avatar-untrained-overlay"
                >
                  <div class="untrained-content">
                    <img
                      src="https://szr.jiajs.cn/index/347.png"
                      alt="克隆"
                      class="clone-icon"
                    />
                    <div class="clone-text">立即克隆</div>
                  </div>
                </div>

                <!-- 状态显示 - 音色列表 -->
                <div v-if="activeTabId === 2" class="voice-status">
                  {{ getVoiceStatusText(item) }}
                </div>

                <!-- 状态显示 - 作品列表 -->
                <div v-if="activeTabId === 3" class="video-status">
                  {{ getVideoStatusText(item) }}
                </div>

                <!-- 状态显示 - 成品库列表 -->
                <div
                  v-if="activeTabId === 4"
                  class="library-status"
                  :class="{
                    'status-processing': item.status < 4,
                    'status-completed': item.status === 4,
                    'status-failed': item.status === 5,
                    'status-partial': item.status === 6,
                  }"
                >
                  {{ getLibraryStatusText(item) }}
                </div>

                <!-- 失败状态文字提示 - 作品列表 -->
                <div
                  v-if="activeTabId === 3 && item.current_status === 'fail'"
                  class="video-message-overlay"
                >
                  <div class="video-message">视频合成失败</div>
                </div>

                <!-- 生成中状态文字提示 - 作品列表 -->
                <div
                  v-if="
                    activeTabId === 3 &&
                    item.current_status !== 'success' &&
                    item.current_status !== 'fail'
                  "
                  class="video-message-overlay"
                >
                  <div class="video-message">生成中，请稍等...</div>
                </div>

                <!-- 播放图标 - 适用于"我的形象"和"我的作品" -->
                <div
                  v-if="
                    (activeTabId === 1 &&
                      isCompletedStatus(item, activeSubTabId) &&
                      item.video_url) ||
                    (activeTabId === 3 &&
                      item.current_status === 'success' &&
                      item.result)
                  "
                  class="play-icon-container"
                >
                  <img
                    src="https://szr.jiajs.cn/index/158.png"
                    alt="播放"
                    class="play-icon"
                  />
                </div>

                <!-- 删除按钮 -->
                <button
                  v-if="activeTabId !== 4 || (item && item.type !== 'library')"
                  class="delete-btn"
                  @click="handleDelete(item, $event)"
                >
                  <svg
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V7H6V19ZM19 4H15.5L14.5 3H9.5L8.5 4H5V6H19V4Z"
                      fill="currentColor"
                    />
                  </svg>
                </button>
              </div>
              <div class="item-info">
                <!-- 标题 -->
                <div class="item-title">
                  {{
                    activeTabId === 1
                      ? item.name || '我的形象'
                      : activeTabId === 2
                        ? item.name
                        : activeTabId === 3
                          ? item.name
                          : activeTabId === 4
                            ? item.title
                            : activeTabId === 5
                              ? item.type_tile || '收藏内容'
                              : item.title
                  }}
                </div>
                <!-- 日期 -->
                <div class="item-date">
                  {{
                    activeTabId === 1 || activeTabId === 2 || activeTabId === 3
                      ? item.create_time
                      : activeTabId === 4
                        ? item.create_time
                        : activeTabId === 5
                          ? item.create_time
                          : item.date
                  }}
                </div>
                <!-- 成品库视频数量 -->
                <div v-if="activeTabId === 4" class="item-video-count">
                  视频数量: {{ item.video_count }}
                </div>

                <!-- AI收藏文本操作按钮 -->
                <div
                  v-if="activeTabId === 5 && activeSubTabId === 1"
                  class="collect-text-actions"
                >
                  <Button
                    size="small"
                    type="primary"
                    @click.stop="openTextDetailModal(item)"
                  >
                    查看详情
                  </Button>
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- 统一分页组件 -->
        <div v-if="listPagination.total > 0" class="pagination-container">
          <Pagination
            v-model:current="listPagination.current"
            :total="listPagination.total"
            :page-size="listPagination.pageSize"
            :show-size-changer="false"
            @change="loadListData"
          />
        </div>
      </div>
    </div>

    <!-- 数字人克隆弹窗 -->
    <Drawer
      v-model:open="drawerVisible"
      title="数字人克隆"
      placement="right"
      :width="400"
      :closable="true"
      @close="closeDrawer"
      class="digital-human-drawer"
    >
      <div class="drawer-content">
        <!-- OSS配置加载状态 -->
        <div v-if="ossConfigLoading" class="oss-loading-section">
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <span class="loading-text">正在初始化上传配置...</span>
          </div>
        </div>

        <!-- 训练模式选择区域 -->
        <div
          class="form-section"
          :class="{ disabled: ossConfigLoading }"
          v-if="showTrainingMode"
        >
          <h3 class="section-title">训练模式</h3>
          <Radio.Group
            v-model:value="formData.trainingMode"
            class="training-mode-group"
            :disabled="ossConfigLoading"
          >
            <Radio :value="1">标清</Radio>
            <Radio :value="2">高清</Radio>
          </Radio.Group>
        </div>

        <!-- 视频上传区域 -->
        <div class="form-section" :class="{ disabled: ossConfigLoading }">
          <h3 class="section-title">上传视频</h3>
          <div class="upload-container">
            <div
              class="upload-area"
              :class="{
                'has-video': formData.videoFile,
                disabled: ossConfigLoading,
              }"
              @click="
                !ossConfigLoading &&
                !formData.videoFile &&
                showVideoRequirement()
              "
            >
              <img
                src="https://szr.jiajs.cn/index/223.png"
                alt="上传背景"
                class="upload-background"
              />

              <!-- 视频预览 -->
              <div v-if="formData.videoFile" class="video-preview">
                <video
                  :src="videoPreviewUrl"
                  controls
                  class="preview-video"
                ></video>
                <Button
                  type="text"
                  danger
                  class="remove-video-btn"
                  @click.stop="handleVideoRemove"
                >
                  ×
                </Button>
              </div>

              <!-- 上传提示 -->
              <div v-else class="upload-hint">
                <div class="upload-icon">📹</div>
                <div class="upload-text">点击上传视频</div>
                <div class="upload-desc">支持 MP4、MOV 等格式</div>
              </div>

              <!-- 上传进度 -->
              <div v-if="uploading" class="upload-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: `${uploadProgress}%` }"
                  ></div>
                </div>
                <div class="progress-text">{{ uploadProgress }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户协议区域 -->
        <div class="form-section" :class="{ disabled: ossConfigLoading }">
          <Checkbox
            v-model:checked="formData.agreeToTerms"
            class="agreement-checkbox"
            :disabled="ossConfigLoading"
          >
            我已阅读并同意
            <a @click="viewUserAgreement" class="agreement-link">
              《{{ cloneSetConfig?.protocol_name || '用户协议' }}》
            </a>
          </Checkbox>
        </div>

        <!-- 操作按钮区域 -->
        <div class="form-section button-section">
          <Button
            type="primary"
            :disabled="!canSubmit || ossConfigLoading"
            :loading="uploading || ossConfigLoading"
            @click="handleSubmit"
            class="submit-btn"
          >
            {{ ossConfigLoading ? '配置中...' : '开始上传' }}
          </Button>
          <Button @click="closeDrawer" class="cancel-btn"> 取消 </Button>
        </div>
      </div>
    </Drawer>

    <!-- 视频要求提示弹窗 -->
    <Modal
      v-model:open="videoRequirementVisible"
      title="数字人视频要求"
      :footer="null"
      :width="480"
      centered
      @cancel="closeVideoRequirement"
      class="video-requirement-modal"
    >
      <div class="video-requirement-content">
        <!-- 说明文字 -->
        <div class="requirement-description">
          请在光线清楚的环境下，使用主摄像头，保持镜头平视按下方要求录制上半身和全身视频。
        </div>

        <!-- 示例图片 -->
        <div class="requirement-image">
          <img
            src="https://szr.jiajs.cn/index/326.png"
            alt="视频要求示例"
            class="example-image"
          />
        </div>

        <!-- 要求列表 -->
        <div class="requirement-list">
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >人脸大小必须小于视频宽度1/2，推荐1/4左右</span
            >
          </div>
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >视频全程必须要有人脸和嘴巴、且不得有遮挡</span
            >
          </div>
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >请使用原始视频，不要进行二剪增加字幕音乐等</span
            >
          </div>
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >视频全程只能有一个人物和嘴型不得多人出镜</span
            >
          </div>
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >支持MP4/MOV格式，像素480P-1080P以内</span
            >
          </div>
          <div class="requirement-item">
            <span class="requirement-icon">●</span>
            <span class="requirement-text"
              >上传的视频要求15秒以上，容量100M以内</span
            >
          </div>
        </div>

        <!-- 上传按钮 -->
        <div class="requirement-footer">
          <Button
            type="primary"
            size="large"
            @click="startVideoUpload"
            class="upload-video-btn"
          >
            立即上传视频
          </Button>
        </div>
      </div>
    </Modal>
    <!-- 视频播放弹窗 -->
    <Modal
      v-model:open="videoPlayerVisible"
      title="视频预览"
      :footer="null"
      :width="640"
      centered
      @cancel="closeVideoPlayer"
      class="video-player-modal"
    >
      <div class="video-player-container">
        <video
          v-if="currentVideoUrl"
          :src="currentVideoUrl"
          controls
          autoplay
          class="video-player"
        ></video>
      </div>
    </Modal>

    <!-- 成品视频列表弹窗 -->
    <ProductVideoListModal
      v-model:visible="productVideoModalVisible"
      :product-id="selectedProductId"
      :product-title="selectedProductTitle"
      :type="1"
    />

    <!-- AI收藏文本详情弹窗 -->
    <Modal
      v-model:open="textDetailModalVisible"
      title="文本详情"
      :width="800"
      centered
      @cancel="closeTextDetailModal"
      class="text-detail-modal"
    >
      <div class="text-detail-content">
        <!-- 原始内容显示 -->
        <div class="original-content-section">
          <h4>原始内容</h4>
          <div class="original-content">
            {{ currentTextItem?.content || '' }}
          </div>
        </div>

        <!-- 编辑区域 -->
        <div class="edit-content-section">
          <h4>编辑内容</h4>
          <textarea
            v-model="editedTextContent"
            class="edit-textarea"
            placeholder="在此编辑内容..."
            rows="8"
          ></textarea>
        </div>
      </div>

      <template #footer>
        <div class="text-detail-footer">
          <Button @click="closeTextDetailModal">取消</Button>
          <Button
            type="default"
            :loading="aiRewriteLoading"
            @click="handleAIRewrite"
          >
            AI改写
          </Button>
          <Button type="default" @click="handleCopyText">复制文案</Button>
          <Button type="primary" @click="handleSaveText">保存</Button>
        </div>
      </template>
    </Modal>
  </Page>
</template>

<style lang="scss" scoped>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 动画效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-digital-human-container {
    gap: 20px;
  }

  .tabs-container {
    padding: 0 4px;
  }

  .tabs-wrapper {
    gap: 4px;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 14px;
  }

  .sub-tabs-container {
    padding: 0 12px;
  }

  .sub-tabs-wrapper {
    gap: 8px;
    padding: 6px 0;
  }

  .sub-tab-item {
    padding: 6px 12px;
    font-size: 13px;
  }

  .cards-container {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .content-grid {
    grid-template-columns: repeat(auto-fill, 240px);
    gap: 16px;
    justify-content: center;
  }

  .collect-text-actions {
    margin-top: 8px;
  }

  .service-card {
    max-width: none;
    height: 140px;
  }

  .card-content {
    gap: 20px;
    padding: 24px;
  }

  .icon-wrapper {
    width: 56px;
    height: 56px;

    svg {
      width: 28px;
      height: 28px;
    }
  }

  .card-title {
    font-size: 20px;
  }

  .card-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .ai-digital-human-container {
    gap: 16px;
  }

  .tabs-container {
    padding: 0 2px;
  }

  .tabs-wrapper {
    flex-wrap: wrap;
    gap: 2px;
  }

  .tab-item {
    min-width: fit-content;
    padding: 8px 12px;
    font-size: 13px;
  }

  .sub-tabs-container {
    padding: 0 8px;
  }

  .sub-tabs-wrapper {
    flex-wrap: wrap;
    gap: 6px;
    padding: 4px 0;
  }

  .sub-tab-item {
    min-width: fit-content;
    padding: 6px 10px;
    font-size: 12px;
  }

  .service-card {
    height: 120px;
  }

  .card-content {
    gap: 16px;
    padding: 20px;
  }

  .icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .card-title {
    font-size: 18px;
  }

  .card-subtitle {
    font-size: 13px;
  }

  .content-grid {
    grid-template-columns: 240px;
    gap: 12px;
    justify-content: center;
  }

  .pagination-wrapper {
    gap: 12px;
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .page-number {
    width: 32px;
    height: 32px;
    font-size: 13px;
  }

  .collect-text-actions {
    margin-top: 6px;
  }

  .text-detail-modal {
    .ant-modal-body {
      padding: 16px;
    }
  }

  .text-detail-footer {
    flex-wrap: wrap;
    gap: 8px;
  }
}

.ai-digital-human-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  // max-width: 1200px;
  margin: 0 auto;
}

.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
  justify-items: center;
}

.service-card {
  position: relative;
  width: 100%;
  max-width: 480px;
  height: 160px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid hsl(var(--border));
  border-radius: 16px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary));
    box-shadow: 0 12px 32px rgb(0 0 0 / 15%);
    transform: translateY(-4px);
  }
}

.digital-human-card {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 10%) 0%,
    hsl(var(--primary) / 5%) 100%
  );

  &:hover {
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 15%) 0%,
      hsl(var(--primary) / 8%) 100%
    );
  }
}

.voice-clone-card {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 8%) 0%,
    hsl(var(--primary) / 12%) 100%
  );

  &:hover {
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 12%) 0%,
      hsl(var(--primary) / 18%) 100%
    );
  }
}

.video-creation-card {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 6%) 0%,
    hsl(var(--primary) / 10%) 100%
  );

  &:hover {
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 10%) 0%,
      hsl(var(--primary) / 16%) 100%
    );
  }
}

.card-content {
  display: flex;
  gap: 24px;
  align-items: center;
  height: 100%;
  padding: 32px;
}

.card-icon {
  flex-shrink: 0;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  transition: all 0.3s ease;

  svg {
    width: 32px;
    height: 32px;
    color: white;
  }
}

.digital-human-icon {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);

  .service-card:hover & {
    box-shadow: 0 8px 24px rgb(255 107 53 / 40%);
    transform: scale(1.1);
  }
}

.voice-clone-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .service-card:hover & {
    box-shadow: 0 8px 24px rgb(118 75 162 / 40%);
    transform: scale(1.1);
  }
}

.video-creation-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  .service-card:hover & {
    box-shadow: 0 8px 24px rgb(79 172 254 / 40%);
    transform: scale(1.1);
  }
}

.card-text {
  flex: 1;
}

.card-title {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: hsl(var(--foreground));
  transition: color 0.3s ease;
}

.card-subtitle {
  margin: 0;
  font-size: 16px;
  color: hsl(var(--muted-foreground));
  transition: color 0.3s ease;
}

.service-card:hover .card-title {
  color: hsl(var(--primary));
}

.service-card:hover .card-subtitle {
  color: hsl(var(--foreground));
}

/* 一级菜单样式 */
.tabs-container {
  padding: 0 8px;
  border-bottom: 1px solid hsl(var(--border));
}

.tabs-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tab-item {
  position: relative;
  padding: 12px 20px;
  margin-bottom: -1px;
  font-size: 15px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  cursor: pointer;
  border-bottom: 2px solid transparent;
  border-radius: 6px 6px 0 0;
  transition: all 0.3s ease;

  &:hover {
    color: hsl(var(--foreground));
    background: hsl(var(--muted) / 30%);
  }

  &.active {
    font-weight: 600;
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 5%);
    border-bottom-color: hsl(var(--primary));
  }
}

/* 二级菜单样式 */
.sub-tabs-container {
  padding: 0 16px;
  margin-bottom: 24px;
  background: hsl(var(--muted) / 20%);
  border-bottom: 1px solid hsl(var(--border));
}

.sub-tabs-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
}

.sub-tab-item {
  position: relative;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    color: hsl(var(--foreground));
    background: hsl(var(--muted) / 40%);
  }

  &.active {
    font-weight: 600;
    color: hsl(var(--primary));
    background: hsl(var(--primary) / 8%);
    border-bottom-color: hsl(var(--primary));
  }
}

/* 内容区域样式 */
.content-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 240px);
  gap: 20px;
  justify-content: center;
}

.content-item {
  overflow: hidden;
  cursor: pointer;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary) / 30%);
    box-shadow: 0 12px 32px hsl(var(--shadow) / 15%);
    transform: translateY(-4px);
  }
}

.item-thumbnail {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: hsl(var(--muted));
}

/* 根据菜单类型设置不同的固定尺寸 */
.content-item.image-type,
.content-item.work-type {
  .item-thumbnail {
    width: 240px;
    height: 427px; /* 我的形象、我的作品使用240px*427px */
  }
}

.content-item.audio-type,
.content-item.library-type {
  .item-thumbnail {
    width: 240px;
    height: 240px; /* 我的音色、成品库使用240px*240px */
  }
}

.content-item.avatar-item {
  .item-thumbnail {
    position: relative;
    width: 240px;
    height: 427px;
  }
}

/* 成品库封面样式 */
.library-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 成品库状态样式 */
.library-status {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  border-radius: 4px;

  /* 根据不同状态设置不同颜色 */
  &.status-processing {
    background: #1890ff; /* 蓝色 - 合成中 */
  }

  &.status-completed {
    background: #52c41a; /* 绿色 - 完成 */
  }

  &.status-failed {
    background: #ff4d4f; /* 红色 - 失败 */
  }

  &.status-partial {
    background: #faad14; /* 橙色 - 部分成功 */
  }
}

/* 视频数量显示样式 */
.item-video-count {
  margin-top: 4px;
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.thumbnail-placeholder {
  width: 48px;
  height: 48px;
  color: hsl(var(--muted-foreground));
  opacity: 0.5;

  svg {
    width: 100%;
    height: 100%;
  }
}

.item-info {
  padding: 16px;
}

.item-title {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.item-date {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 删除按钮样式 */
.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
  background: rgb(255 255 255 / 95%);
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  opacity: 0;
  transition: all 0.3s ease;

  svg {
    width: 16px;
    height: 16px;
    color: #ef4444;
  }

  &:hover {
    background: rgb(255 255 255 / 100%);
    box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
    transform: scale(1.1);
  }
}

.content-item:hover .delete-btn {
  opacity: 1;
}

/* 暗色主题下的删除按钮 */
.dark .delete-btn {
  background: rgb(0 0 0 / 85%);

  &:hover {
    background: rgb(0 0 0 / 95%);
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

/* 数字人克隆弹窗样式 */
.digital-human-drawer {
  .drawer-content {
    padding: 0;
  }
}

/* OSS配置加载状态样式 */
.oss-loading-section {
  padding: 20px;
  margin-bottom: 20px;
  background: hsl(var(--muted) / 30%);
  border-radius: 8px;
}

.loading-content {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid hsl(var(--border));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 禁用状态样式 */
.form-section.disabled {
  pointer-events: none;
  opacity: 0.6;
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.form-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

/* 训练模式选择样式 */
.training-mode-group {
  display: flex;
  gap: 16px;

  .ant-radio-wrapper {
    font-size: 14px;
  }
}

/* 视频上传样式 */
.upload-container {
  display: flex;
  justify-content: center;
}

.video-upload {
  .ant-upload {
    display: block;
  }
}

.upload-area {
  position: relative;
  width: 250px;
  height: 445px;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: hsl(var(--primary));
    box-shadow: 0 4px 12px hsl(var(--primary) / 20%);
  }

  &.has-video {
    border-color: hsl(var(--primary));
  }
}

.upload-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 2;
  width: 240px;
  height: 427px;
  overflow: hidden;
  border-radius: 6px;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-video-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
  background: rgb(255 255 255 / 90%);
  border-radius: 50%;

  &:hover {
    background: rgb(255 255 255 / 100%);
  }
}

.upload-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  color: hsl(var(--muted-foreground));
  text-align: center;
  transform: translate(-50%, -50%);
}

.upload-icon {
  margin-bottom: 16px;
  font-size: 48px;
}

.upload-text {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.upload-desc {
  font-size: 12px;
  color: hsl(var(--muted-foreground));
}

.upload-progress {
  position: absolute;
  right: 20px;
  bottom: 20px;
  left: 20px;
  z-index: 3;
}

.progress-bar {
  width: 100%;
  height: 4px;
  margin-bottom: 8px;
  overflow: hidden;
  background: rgb(255 255 255 / 30%);
  border-radius: 2px;
}

.progress-fill {
  height: 100%;
  background: hsl(var(--primary));
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-align: center;
}

/* 用户协议样式 */
.agreement-checkbox {
  font-size: 14px;

  .agreement-link {
    color: hsl(var(--primary));
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

/* 按钮区域样式 */
.button-section {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid hsl(var(--border));
}

.submit-btn {
  min-width: 100px;
}

.cancel-btn {
  min-width: 80px;
}

/* 视频要求提示弹窗样式 */
.video-requirement-modal {
  .ant-modal-header {
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }
}

.video-requirement-content {
  padding: 20px 0;
}

.requirement-description {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.6;
  color: hsl(var(--muted-foreground));
  text-align: center;
}

.requirement-image {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.example-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px hsl(var(--shadow) / 15%);
}

.requirement-list {
  margin-bottom: 32px;
}

.requirement-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;

  &:last-child {
    margin-bottom: 0;
  }
}

.requirement-icon {
  flex-shrink: 0;
  margin-top: 2px;
  margin-right: 8px;
  font-size: 12px;
  color: hsl(var(--primary));
}

.requirement-text {
  flex: 1;
  color: hsl(var(--foreground));
}

.requirement-footer {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid hsl(var(--border));
}

.upload-video-btn {
  min-width: 160px;
  height: 44px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 80%) 100%
  );
  border: none;
  border-radius: 8px;

  &:hover {
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 90%) 0%,
      hsl(var(--primary) / 70%) 100%
    );
    box-shadow: 0 6px 16px hsl(var(--primary) / 30%);
    transform: translateY(-1px);
  }
}

/* 暗色主题适配 */
.dark .service-card {
  border-color: hsl(var(--border));

  &:hover {
    border-color: hsl(var(--primary));
    box-shadow: 0 12px 32px rgb(0 0 0 / 30%);
  }
}

.dark .content-item {
  &:hover {
    box-shadow: 0 12px 32px rgb(0 0 0 / 20%);
  }
}

.dark .remove-video-btn {
  color: #ff4d4f;
  background: rgb(0 0 0 / 80%);

  &:hover {
    background: rgb(0 0 0 / 90%);
  }
}

.service-card:active {
  transform: translateY(-2px);
  transition: transform 0.1s ease;
}

.service-card:active .icon-wrapper {
  transform: scale(1.05);
}

/* 我的形象项特定样式 */
.avatar-item {
  .item-thumbnail {
    position: relative;
    width: 240px;
    height: 427px;
  }
}

.avatar-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-status {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: rgb(0 0 0 / 70%);
  border-radius: 4px;
}

.avatar-untrained-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 0 0 / 60%);
}

.untrained-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.clone-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}

.clone-text {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

/* 视频播放弹窗样式 */
.video-player-modal {
  .ant-modal-header {
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .ant-modal-body {
    padding: 16px;
  }
}

.video-player-container {
  display: flex;
  justify-content: center;
  width: 100%;
  aspect-ratio: 16/9;
  overflow: hidden;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.voice-item {
  .item-thumbnail {
    position: relative;
    width: 240px;
    height: 240px;
  }
}

.voice-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.voice-status {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: rgb(0 0 0 / 70%);
  border-radius: 4px;
}

.video-status {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: rgb(0 0 0 / 70%);
  border-radius: 4px;
}

.play-icon-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: none;
}

.play-icon {
  width: 48px;
  height: 48px;
  pointer-events: auto;
}

/* 视频过期提示样式 */
.video-expire-notice {
  width: 100%;
  padding: 8px 16px;
  margin-top: 8px;
  font-size: 13px;
  color: #ff6b35;
  text-align: center;
  background: rgb(255 107 53 / 10%);
  border-radius: 4px;
}

/* 视频状态文字提示样式 */
.video-message-overlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 0 0 / 40%);
}

.video-message {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  text-align: center;
}

/* AI收藏相关样式 */
.collect-item {
  .item-thumbnail {
    position: relative;
    width: 240px;
    height: 240px; /* AI收藏使用正方形缩略图 */
  }
}

.collect-text-content {
  position: absolute;
  top: 0;
  left: 0;
  display: -webkit-box;
  width: 100%;
  height: 100%;
  padding: 16px;
  overflow: hidden;
  text-overflow: ellipsis; /* 溢出显示省略号 */
  -webkit-line-clamp: 3; /* 最多显示3行 */
  font-size: 14px;
  line-height: 1.5;
  color: hsl(var(--foreground));
  word-wrap: break-word;
  overflow-wrap: break-word; /* 使用现代CSS标准，避免强制断行 */
  background: hsl(var(--muted) / 30%);
  -webkit-box-orient: vertical;
}

.collect-image-cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.collect-video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;

  &::-webkit-media-controls-panel {
    background: linear-gradient(transparent, rgb(0 0 0 / 80%));
  }

  &::-webkit-media-controls-play-button,
  &::-webkit-media-controls-volume-slider,
  &::-webkit-media-controls-timeline,
  &::-webkit-media-controls-current-time-display,
  &::-webkit-media-controls-time-remaining-display {
    filter: brightness(1.2);
  }
}

.collect-text-actions {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

/* 文本详情弹窗样式 */
.text-detail-modal {
  .ant-modal-header {
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .ant-modal-body {
    padding: 24px;
  }
}

.text-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.original-content-section,
.edit-content-section {
  h4 {
    margin: 0 0 12px;
    font-size: 16px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }
}

.original-content {
  max-height: 200px;
  padding: 16px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
  color: hsl(var(--foreground));
  word-wrap: break-word;
  white-space: pre-wrap;
  background: hsl(var(--muted) / 30%);
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
}

.edit-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  color: hsl(var(--foreground));
  resize: vertical;
  background: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;

  &:focus {
    outline: none;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 2px hsl(var(--primary) / 20%);
  }
}

.text-detail-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}
</style>
