<script setup lang="ts">
import type {
  CopywritingItem,
  TitleItem,
  VideoMaterialItem,
  VideoTaskItem,
  VideoTaskListParams,
} from '#/api/core';

import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';
// import { useI18n } from '@vben/locales'; // 暂时未使用

import {
  Avatar as AAvatar,
  Button as AButton,
  Card as ACard,
  Cascader as ACascader,
  Checkbox as ACheckbox,
  CheckboxGroup as ACheckboxGroup,
  Col as ACol,
  DatePicker as ADatePicker,
  Drawer as ADrawer,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Modal as AModal,
  Pagination as APagination,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Step as AStep,
  Steps as ASteps,
  TabPane as ATabPane,
  Tabs as ATabs,
  message,
} from 'ant-design-vue';

import {
  addVideoTask,
  getAccountGroupList,
  getAccountList,
  getCopywritingList,
  getMatrixStatistics,
  getTitleList,
  getVideoMaterialList,
  getVideoTaskList,
  searchPoiList,
  updateCopywriting,
  updateTitle,
} from '#/api/core';
import zones_tree from '#/utils/city';
import { createStreamHandler } from '#/utils/stream-request';

import ProductVideoListModal from '../../ai-digital-human/components/ProductVideoListModal.vue';
import AddAccountModal from './components/AddAccountModal.vue';
import TaskCard from './components/TaskCard.vue';

// const { t } = useI18n(); // 暂时未使用
const router = useRouter();

// 统计数据
const statsData = ref({
  account_count: 0, // 累计发布账户
  douyin_account_count: 0, // 抖音账户
  kuaishou_account_count: 0, // 快手账户
  shipinghao_account_count: 0, // 视频号账户
  xiaohong_account_count: 0, // 小红薯账户
  exposure_count_total: 0, // 累计曝光
  like_count_total: 0, // 累计点赞
  comment_total: 0, // 累计评价
});

const statsLoading = ref(false);

// 功能模块数据
const moduleData = ref([
  {
    key: 'accountList',
    title: '账号列表',
    description: '查看和管理所有已添加的社交媒体账号',
    icon: '👥',
    route: '/public-domain/ai-matrix-management/account-management',
  },
  {
    key: 'groupManagement',
    title: '分组管理',
    description: '创建和管理账号分组，便于批量操作',
    icon: '📁',
    route: '/public-domain/ai-matrix-management/group-management',
  },
  {
    key: 'publishVideo',
    title: '发布视频',
    description: '快速发布视频到各个平台，支持批量操作和定时发布',
    icon: '🎬',
    route: '/public-domain/ai-matrix-management/publish-management',
  },
  {
    key: 'addAccount',
    title: '添加账户',
    description: '添加新的社交媒体账户到矩阵管理系统',
    icon: '➕',
    route: '/public-domain/ai-matrix-management/add-account',
  },
]);

// 处理模块点击
const handleModuleClick = (module: any) => {
  // console.log('点击模块:', module.key);

  // 发布视频卡片特殊处理：直接打开发布弹窗
  if (module.key === 'publishVideo') {
    handlePublishVideo();
  } else if (module.key === 'addAccount') {
    // 添加账户卡片特殊处理：直接打开添加账户弹窗
    handleAddAccount();
  } else if (module.route) {
    router.push(module.route);
  }
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    statsLoading.value = true;
    const response = await getMatrixStatistics();
    statsData.value = response;
  } catch (error) {
    console.error('加载统计数据失败:', error);
    // 保持默认值，不影响用户体验
  } finally {
    statsLoading.value = false;
  }
};

// 加载任务列表
const loadTaskList = async () => {
  try {
    const params: VideoTaskListParams = {
      page: taskPagination.value.current,
      psize: taskPagination.value.pageSize,
      type: activeTaskTab.value, // ""为空是全部，1抖音2快手3视频号4小红书
    };

    const response = await getVideoTaskList(params);
    taskList.value = response.list;
    taskPagination.value.total = response.total;
  } catch (error) {
    console.error('加载任务列表失败:', error);
    message.error('加载任务列表失败，请重试');
    taskList.value = [];
    taskPagination.value.total = 0;
  }
};

// 处理任务分页变化
const handleTaskPageChange = (page: number, pageSize: number) => {
  taskPagination.value.current = page;
  taskPagination.value.pageSize = pageSize;
  loadTaskList();
};

// 处理任务平台标签页切换
const handleTaskTabChange = (key: string) => {
  activeTaskTab.value = key;
  taskPagination.value.current = 1; // 切换标签页时重置为第一页
  loadTaskList();
};

// 处理任务操作
const handleTaskAction = (action: string, _task: any) => {
  // console.log(`${action} 任务:`, task.name);
  // 这里可以添加具体的任务操作逻辑
  switch (action) {
    case 'complete': {
      // console.log('标记为已完成');
      break;
    }
    case 'delete': {
      // console.log('删除任务');
      break;
    }
    case 'refresh': {
      // 刷新任务列表
      // console.log('刷新任务列表');
      loadTaskList();
      break;
    }
    case 'stop': {
      // console.log('终止任务');
      break;
    }
    case 'terminate': {
      // console.log('标记为已终止');
      break;
    }
    case 'view': {
      // console.log('查看任务详情');
      break;
    }
    default:
    // console.log('未知操作:', action);
  }
};

// 格式化数字显示
const formatNumber = (num: number) => {
  if (num >= 10_000) {
    return `${(num / 10_000).toFixed(1)}万`;
  }
  return num.toLocaleString();
};

// 任务相关数据
const taskList = ref<VideoTaskItem[]>([]);

// 任务平台筛选标签页数据
const taskTabsData = ref([
  { key: '', label: '全部' },
  { key: '1', label: '抖音' },
  { key: '2', label: '快手' },
  { key: '3', label: '视频号' },
  { key: '4', label: '小红书' },
]);

// 当前选中的任务平台标签页
const activeTaskTab = ref('');
const taskPagination = ref({
  current: 1,
  pageSize: 8, // 一行4个，两行8个
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
});

// 发布视频相关状态
const platformSelectVisible = ref(false);
const publishModalVisible = ref(false);
const currentStep = ref(0);
const selectedPlatform = ref<null | number>(null);

// 添加账户弹窗状态
const addAccountModalVisible = ref(false);

// 表单数据
const formData = ref({
  taskName: '',
  selectedMaterial: '',
  selectedMaterialId: null as null | number,
  videoCount: 0,
  selectedTitles: [] as string[],
  selectedTitleIds: [] as number[],
  titleList: [{ title: '' }] as Array<{ title: string }>, // 视频号专用标题输入
  mountType: '不挂载',
  city: [] as string[],
  merchantName: '',
  poiAddress: '',
  poiId: '', // POI ID
  poiName: '', // POI名称
  appId: '',
  miniProgramName: '',
  miniProgramUrl: '',
  cartId: '',
  productId: '',
  publishType: '随机发布',
  startDate: null,
  dailyCount: 1,
  publishDays: 1,
  accountGroup: undefined as number | undefined,
  selectedAccounts: [] as number[],
  // 小红书特有字段
  copywiring: '', // 文案ID
  goodsId: '', // 商品ID
});

// 平台选项 - 统一使用数字映射
const platformOptions = [
  { value: 1, label: '抖音', icon: '🎵' },
  { value: 2, label: '快手', icon: '⚡' },
  { value: 3, label: '视频号', icon: '📱' },
  { value: 4, label: '小红书', icon: '📖' },
];

// 城市数据（从city.ts导入，只保留省-市两级）
const cityOptions = ref(
  zones_tree.map((province) => ({
    value: province.code,
    label: province.name,
    children: province.children.map((city) => ({
      value: city.code,
      label: city.name,
    })),
  })),
);

// POI相关状态
const poiOptions = ref<{ label: string; value: string }[]>([]);
const poiLoading = ref(false);

// 时间选择相关数据
const dayData = [
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
  '24:00',
];

// 生成未来7天的日期
const generateSevenDays = () => {
  const days = [];
  const today = new Date();
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD格式
    let dayName;
    if (i === 0) {
      dayName = '今天';
    } else if (i === 1) {
      dayName = '明天';
    } else {
      dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][
        date.getDay()
      ];
    }
    days.push({
      date: dateStr,
      label: `${dateStr} (${dayName})`,
      dayName,
    });
  }
  return days;
};

const availableDays = ref(generateSevenDays());

// 账户时间选择状态 - 格式: { accountId: { date: [selectedTimes] } }
const accountTimeSelections = ref<Record<number, Record<string, string[]>>>({});

// 时间选择弹窗状态
const timeSelectionModalVisible = ref(false);
const currentEditingAccount = ref<null | number>(null);

// 临时时间选择状态（弹窗中的选择，未确认前不保存到正式状态）
const tempTimeSelections = ref<Record<string, string[]>>({});

// 动态分组和账户数据
const groupOptions = ref<{ label: string; value: number }[]>([]);
const accountOptions = ref<
  {
    avatar?: string;
    disabled?: boolean;
    expired?: boolean;
    expiresIn?: string;
    label: string;
    value: number;
  }[]
>([]);

// 原始账户数据（用于任务生成）
const rawAccountData = ref<any[]>([]);
const groupLoading = ref(false);
const accountLoading = ref(false);

// 挂载类型映射
const mountTypeMap: Record<string, number> = {
  商家POI: 1,
  小程序: 2,
  不挂载: 3,
};

// 发布类型映射
const releaseTypeMap: Record<string, number> = {
  随机发布: 1,
  指定时间发布: 2,
};

// 抽屉状态
const materialDrawerVisible = ref(false);
const titleDrawerVisible = ref(false);
const copywritingDrawerVisible = ref(false);

// 素材和标题数据
const materialList = ref<VideoMaterialItem[]>([]);
const titleList = ref<TitleItem[]>([]);
const materialLoading = ref(false);
const titleLoading = ref(false);
const copywritingLoading = ref(false);
const selectedMaterial = ref<null | VideoMaterialItem>(null);
const selectedTitles = ref<TitleItem[]>([]);
const selectedTitleIds = ref<number[]>([]);
const selectedCopywriting = ref<CopywritingItem | null>(null);

// 成品视频列表弹窗状态
const productVideoModalVisible = ref(false);
const selectedProductId = ref(0);
const selectedProductTitle = ref('');

// 编辑弹窗状态
const titleEditModalVisible = ref(false);
const copywritingEditModalVisible = ref(false);
const currentEditingTitle = ref<null | TitleItem>(null);
const currentEditingCopywriting = ref<CopywritingItem | null>(null);

// 编辑表单数据
const titleEditForm = ref({
  answer: '',
  challenges: '',
});
const copywritingEditForm = ref({
  content: '',
});

// AI改写加载状态
const titleAnswerRewriteLoading = ref(false);
const titleChallengesRewriteLoading = ref(false);
const copywritingRewriteLoading = ref(false);

// 分页状态
const materialPagination = ref({
  current: 1,
  pageSize: 12,
  total: 0,
});

const titlePagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

const copywritingList = ref<CopywritingItem[]>([]);
const copywritingPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 表单验证规则
const formRules = {
  taskName: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 30, message: '任务名称不能超过30个字符', trigger: 'blur' },
  ],
  selectedMaterial: [
    { required: true, message: '请选择素材', trigger: 'change' },
  ],
  selectedTitles: [
    { required: true, message: '请选择标题', trigger: 'change' },
  ],
  titleList: [
    { required: true, message: '请至少输入一个标题', trigger: 'change' },
    {
      validator: (rule: any, value: Array<{ title: string }>) => {
        if (!value || value.length === 0) {
          return Promise.reject('请至少输入一个标题');
        }
        const hasValidTitle = value.some(
          (item) => item.title && item.title.trim(),
        );
        if (!hasValidTitle) {
          return Promise.reject('请至少输入一个有效标题');
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ],
  // 抖音平台特有字段验证
  city: [{ required: true, message: '请选择城市', trigger: 'change' }],
  merchantName: [
    { required: true, message: '请输入商家简称', trigger: 'blur' },
  ],
  poiAddress: [{ required: true, message: '请选择POI地址', trigger: 'change' }],
  // 快手平台特有字段验证
  appId: [
    { required: true, message: '请输入快手小程序APPID', trigger: 'blur' },
  ],
  miniProgramName: [
    { required: true, message: '请输入快手小程序名称', trigger: 'blur' },
  ],
  miniProgramUrl: [
    { required: true, message: '请输入快手小程序URL', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
  ],
  // 小红书平台特有字段验证（商品ID不是必填）
  productId: [
    { pattern: /^\d+$/, message: '商品ID必须为数字', trigger: 'blur' },
  ],
  // 第二步验证规则
  publishType: [
    { required: true, message: '请选择发布时间类型', trigger: 'change' },
  ],
  startDate: [
    { required: true, message: '请选择开始发布日期', trigger: 'change' },
  ],
  dailyCount: [
    { required: true, message: '请输入每天发布数量', trigger: 'blur' },
    {
      type: 'number',
      min: 1,
      max: 35,
      message: '每天发布数量必须在1-35之间',
      trigger: 'blur',
    },
  ],
  publishDays: [
    { required: true, message: '请输入发布天数', trigger: 'blur' },
    { type: 'number', min: 1, message: '发布天数必须大于0', trigger: 'blur' },
  ],
  accountGroup: [
    { required: true, message: '请选择账户分组', trigger: 'change' },
  ],
  selectedAccounts: [
    { required: true, message: '请选择账户', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个账户', trigger: 'change' },
  ],
};

// 发布视频相关方法
const handlePublishVideo = () => {
  platformSelectVisible.value = true;
};

// 添加账户相关方法
const handleAddAccount = () => {
  addAccountModalVisible.value = true;
};

const handleAddAccountSuccess = () => {
  // 添加账户成功后的处理
  message.success('账户添加成功');
  // 可以在这里刷新相关数据
  loadStatistics();
};

const handlePlatformSelect = (platform: number) => {
  selectedPlatform.value = platform;
  platformSelectVisible.value = false;
  publishModalVisible.value = true;
  currentStep.value = 0;
  // 重置表单数据
  resetFormData();
  // 初始化POI相关数据
  poiOptions.value = [];
  // 预加载素材列表以检查是否有可用素材
  loadMaterialList();
  // 根据平台加载对应的分组和账户
  loadAccountGroups();
};

const resetFormData = () => {
  formData.value = {
    taskName: '',
    selectedMaterial: '',
    selectedMaterialId: null,
    videoCount: 0,
    selectedTitles: [],
    selectedTitleIds: [],
    titleList: [{ title: '' }], // 重置视频号标题输入
    mountType: '不挂载',
    city: [],
    merchantName: '',
    poiAddress: '',
    poiId: '', // POI ID
    poiName: '', // POI名称
    appId: '',
    miniProgramName: '',
    miniProgramUrl: '',
    cartId: '',
    productId: '',
    publishType: '随机发布',
    startDate: null,
    dailyCount: 1,
    publishDays: 1,
    accountGroup: undefined,
    selectedAccounts: [],
    // 小红书特有字段
    copywiring: '', // 文案ID
    goodsId: '', // 商品ID
  };

  // 重置其他相关状态
  selectedMaterial.value = null;
  selectedTitles.value = [];
  selectedTitleIds.value = [];
  selectedCopywriting.value = null;

  // 重置分页状态
  materialPagination.value = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  titlePagination.value = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  copywritingPagination.value = {
    current: 1,
    pageSize: 10,
    total: 0,
  };

  // 重置列表数据
  materialList.value = [];
  titleList.value = [];
  copywritingList.value = [];

  // 重置账户相关数据
  groupOptions.value = [];
  accountOptions.value = [];
  rawAccountData.value = [];

  // 重置时间选择数据
  accountTimeSelections.value = {};
  availableDays.value = generateSevenDays();

  // 重置POI数据
  poiOptions.value = [];

  // 重置抽屉状态
  materialDrawerVisible.value = false;
  titleDrawerVisible.value = false;
  copywritingDrawerVisible.value = false;
  timeSelectionModalVisible.value = false;

  // 重置当前步骤
  currentStep.value = 0;
};

const handleNextStep = () => {
  if (currentStep.value < 1) {
    currentStep.value++;
  }
};

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const handleSubmit = async () => {
  try {
    // 获取用户ID (需要从实际登录状态获取)
    const uid = 7; // TODO: 从登录状态获取实际用户ID

    // 生成发布任务数组
    let scheduleTimes = [];
    scheduleTimes =
      formData.value.publishType === '指定时间发布'
        ? generateSpecificTasks()
        : generateRandomTasks();

    // 验证任务数量是否超过视频数量
    if (!validateTaskCount(scheduleTimes)) {
      return; // 验证失败，停止提交
    }

    // console.log('生成的任务数组:', scheduleTimes);
    // console.log('任务总数:', scheduleTimes.length);
    // console.log('视频总数:', formData.value.videoCount);

    // 根据平台类型构建提交数据
    const platformType = selectedPlatform.value!;

    // 将startDate转换为字符串格式
    let startDateStr = '';
    if (formData.value.startDate) {
      startDateStr =
        typeof formData.value.startDate === 'string'
          ? formData.value.startDate
          : formData.value.startDate.toISOString().split('T')[0];
    }

    const submitData: any = {
      uid,
      name: formData.value.taskName,
      clip_id: formData.value.selectedMaterialId,
      title_id: formData.value.selectedTitleIds.join(','),
      release_type: releaseTypeMap[formData.value.publishType],
      set_time: scheduleTimes,
      startDate: startDateStr,
      release_everyday_count: formData.value.dailyCount,
      release_day_count: formData.value.publishDays,
      type: platformType,
    };

    // 视频号平台特殊处理：添加title字段，title_id改为可选
    if (platformType === 3) {
      submitData.title = formData.value.titleList.filter(
        (item) => item.title && item.title.trim(),
      );
      submitData.title_id = ''; // 视频号不使用title_id
    }

    // 添加平台特有字段
    if (platformType === 1) {
      // 抖音
      submitData.mount_type = mountTypeMap[formData.value.mountType];
      if (formData.value.mountType === '商家POI') {
        submitData.poi = formData.value.poiId;
        submitData.poi_address = formData.value.poiName;
      }
    }

    if (platformType === 2) {
      // 快手
      submitData.merchant_product_id = formData.value.cartId;
    }

    if (platformType === 4) {
      // 小红书
      submitData.copywiring = formData.value.copywiring;
      submitData.goods_id = formData.value.goodsId;
    }

    // console.log('提交数据:', submitData);

    // 调用发布任务API，响应拦截器直接返回data
    await addVideoTask(submitData);

    // API调用成功，显示成功提示
    message.success('发布任务创建成功！');
    publishModalVisible.value = false;
    // 重置表单数据到原始状态
    resetFormData();
  } catch (error) {
    console.error('提交失败:', error);
    message.error('提交失败，请重试');
  }
};

const handleCancel = () => {
  publishModalVisible.value = false;
  platformSelectVisible.value = false;
  // 重置表单数据到原始状态
  resetFormData();
};

// 加载素材数据
const loadMaterialList = async () => {
  try {
    materialLoading.value = true;
    const params = {
      is_publishe: selectedPlatform.value?.toString() || '1',
      page: materialPagination.value.current,
      psize: materialPagination.value.pageSize,
      status: 1,
      uid: 7,
    };

    const response = await getVideoMaterialList(params);
    materialList.value = response.list;
    materialPagination.value.total = response.total;
  } catch (error) {
    console.error('加载素材列表失败:', error);
  } finally {
    materialLoading.value = false;
  }
};

// 加载标题数据
const loadTitleList = async () => {
  try {
    titleLoading.value = true;
    const params = {
      page: titlePagination.value.current,
      psize: titlePagination.value.pageSize,
      uid: 7,
    };

    const response = await getTitleList(params);
    titleList.value = response.list;
    titlePagination.value.total = response.total;
  } catch (error) {
    console.error('加载标题列表失败:', error);
  } finally {
    titleLoading.value = false;
  }
};

const loadCopywritingList = async () => {
  try {
    copywritingLoading.value = true;
    const params = {
      page: copywritingPagination.value.current,
      psize: copywritingPagination.value.pageSize,
      uid: 7,
    };

    const response = await getCopywritingList(params);
    copywritingList.value = response.list; // API返回的数据结构中data是数组
    copywritingPagination.value.total = response.total;
  } catch (error) {
    console.error('加载文案列表失败:', error);
  } finally {
    copywritingLoading.value = false;
  }
};

// 抽屉相关方法
const openMaterialDrawer = () => {
  materialDrawerVisible.value = true;
  loadMaterialList();
};

const openTitleDrawer = () => {
  titleDrawerVisible.value = true;
  loadTitleList();
};

const openCopywritingDrawer = () => {
  copywritingDrawerVisible.value = true;
  loadCopywritingList();
};

const closeMaterialDrawer = () => {
  materialDrawerVisible.value = false;
};

const closeTitleDrawer = () => {
  titleDrawerVisible.value = false;
};

// 打开成品视频列表弹窗
const openProductVideoModal = (material: VideoMaterialItem) => {
  if (material.status === 4) {
    selectedProductId.value = material.id;
    selectedProductTitle.value = material.title;
    productVideoModalVisible.value = true;
  } else {
    message.info('只有完成状态的成品才能查看视频列表');
  }
};

// 素材选择方法
const handleMaterialConfirm = () => {
  if (selectedMaterial.value) {
    // 检查选中素材的视频数量
    if (selectedMaterial.value.video_count === 0) {
      message.warning('所选素材的视频数量为0，无法使用，请选择其他素材');
      return;
    }

    // 确认选择素材
    formData.value.selectedMaterial = selectedMaterial.value.title;
    formData.value.selectedMaterialId = selectedMaterial.value.id;
    formData.value.videoCount = selectedMaterial.value.video_count;
    materialDrawerVisible.value = false;

    message.success(
      `已选择素材：${selectedMaterial.value.title}（${selectedMaterial.value.video_count}个视频）`,
    );
  }
};

// 标题选择方法
const handleTitleConfirm = () => {
  const selectedTitleObjects = titleList.value.filter((title) =>
    selectedTitleIds.value.includes(title.id),
  );
  formData.value.selectedTitles = selectedTitleObjects.map(
    (t) => `${t.answer}${t.challenges}`,
  );
  formData.value.selectedTitleIds = selectedTitleIds.value;
  titleDrawerVisible.value = false;
};

// 文案选择方法
const handleCopywritingConfirm = () => {
  if (selectedCopywriting.value) {
    formData.value.copywiring = selectedCopywriting.value.id.toString();
    copywritingDrawerVisible.value = false;
  }
};

// 视频号标题输入相关方法
const addTitleItem = () => {
  if (formData.value.titleList.length < 10) {
    formData.value.titleList.push({ title: '' });
  }
};

const removeTitleItem = (index: number) => {
  if (formData.value.titleList.length > 1) {
    formData.value.titleList.splice(index, 1);
    validateTitleList();
  }
};

const validateTitleList = () => {
  // 触发表单验证
  nextTick(() => {
    // 这里可以添加额外的验证逻辑
  });
};

// 打开标题编辑弹窗
const openTitleEditModal = (title: TitleItem) => {
  currentEditingTitle.value = title;
  titleEditForm.value = {
    answer: title.answer,
    challenges: title.challenges,
  };
  titleEditModalVisible.value = true;
};

// 关闭标题编辑弹窗
const closeTitleEditModal = () => {
  titleEditModalVisible.value = false;
  currentEditingTitle.value = null;
  titleEditForm.value = {
    answer: '',
    challenges: '',
  };
  titleAnswerRewriteLoading.value = false;
  titleChallengesRewriteLoading.value = false;
};

// 打开文案编辑弹窗
const openCopywritingEditModal = (copywriting: CopywritingItem) => {
  currentEditingCopywriting.value = copywriting;
  copywritingEditForm.value = {
    content: copywriting.answer,
  };
  copywritingEditModalVisible.value = true;
};

// 关闭文案编辑弹窗
const closeCopywritingEditModal = () => {
  copywritingEditModalVisible.value = false;
  currentEditingCopywriting.value = null;
  copywritingEditForm.value = {
    content: '',
  };
  copywritingRewriteLoading.value = false;
};

// AI改写标题
const handleTitleAnswerRewrite = async () => {
  if (!titleEditForm.value.answer.trim()) {
    message.warning('请输入要改写的标题内容');
    return;
  }

  try {
    titleAnswerRewriteLoading.value = true;

    // 清空当前内容
    titleEditForm.value.answer = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: '/mobile/ai_dialogue/spread',
      method: 'POST',
      body: {
        content: currentEditingTitle.value?.answer || '',
        type: 1, // 标题改写
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            titleEditForm.value.answer += jsonData.data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到内容中
          titleEditForm.value.answer += data;
        }
      },
      onComplete: () => {
        message.success('标题AI改写完成');
        titleAnswerRewriteLoading.value = false;
      },
      onError: (error: Error) => {
        console.error('标题AI改写失败:', error);
        message.error(`标题AI改写失败: ${error.message}`);
        titleAnswerRewriteLoading.value = false;
      },
    });
  } catch (error) {
    console.error('标题AI改写失败:', error);
    message.error('标题AI改写失败，请重试');
    titleAnswerRewriteLoading.value = false;
  }
};

// AI改写话题
const handleTitleChallengesRewrite = async () => {
  if (!titleEditForm.value.challenges.trim()) {
    message.warning('请输入要改写的话题内容');
    return;
  }

  try {
    titleChallengesRewriteLoading.value = true;

    // 清空当前内容
    titleEditForm.value.challenges = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: '/mobile/ai_dialogue/spread',
      method: 'POST',
      body: {
        content: currentEditingTitle.value?.challenges || '',
        type: 2, // 话题改写
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            titleEditForm.value.challenges += jsonData.data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到内容中
          titleEditForm.value.challenges += data;
        }
      },
      onComplete: () => {
        message.success('话题AI改写完成');
        titleChallengesRewriteLoading.value = false;
      },
      onError: (error: Error) => {
        console.error('话题AI改写失败:', error);
        message.error(`话题AI改写失败: ${error.message}`);
        titleChallengesRewriteLoading.value = false;
      },
    });
  } catch (error) {
    console.error('话题AI改写失败:', error);
    message.error('话题AI改写失败，请重试');
    titleChallengesRewriteLoading.value = false;
  }
};

// AI改写文案
const handleCopywritingRewrite = async () => {
  if (!copywritingEditForm.value.content.trim()) {
    message.warning('请输入要改写的文案内容');
    return;
  }

  try {
    copywritingRewriteLoading.value = true;

    // 清空当前内容
    copywritingEditForm.value.content = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: '/mobile/ai_dialogue/spread',
      method: 'POST',
      body: {
        content: currentEditingCopywriting.value?.answer || '',
        type: 3, // 文案改写
      },
      onData: (data: string) => {
        try {
          // 尝试解析JSON数据
          const jsonData = JSON.parse(data);
          if (jsonData && jsonData.data) {
            copywritingEditForm.value.content += jsonData.data;
          }
        } catch {
          // 如果不是JSON格式，则直接添加到内容中
          copywritingEditForm.value.content += data;
        }
      },
      onComplete: () => {
        message.success('文案AI改写完成');
        copywritingRewriteLoading.value = false;
      },
      onError: (error: Error) => {
        console.error('文案AI改写失败:', error);
        message.error(`文案AI改写失败: ${error.message}`);
        copywritingRewriteLoading.value = false;
      },
    });
  } catch (error) {
    console.error('文案AI改写失败:', error);
    message.error('文案AI改写失败，请重试');
    copywritingRewriteLoading.value = false;
  }
};

// 保存标题
const handleTitleSave = async () => {
  if (
    !titleEditForm.value.answer.trim() ||
    !titleEditForm.value.challenges.trim()
  ) {
    message.warning('请填写完整的标题和话题内容');
    return;
  }

  if (!currentEditingTitle.value?.id) {
    message.error('缺少必要的保存信息');
    return;
  }

  try {
    const params = {
      answer: titleEditForm.value.answer,
      challenges: titleEditForm.value.challenges,
      id: currentEditingTitle.value.id,
    };

    await updateTitle(params);
    message.success('标题保存成功');

    // 更新当前项的内容
    if (currentEditingTitle.value) {
      currentEditingTitle.value.answer = titleEditForm.value.answer;
      currentEditingTitle.value.challenges = titleEditForm.value.challenges;
    }

    // 刷新列表
    await loadTitleList();

    closeTitleEditModal();
  } catch (error) {
    console.error('标题保存失败:', error);
    message.error('标题保存失败，请重试');
  }
};

// 保存文案
const handleCopywritingSave = async () => {
  if (!copywritingEditForm.value.content.trim()) {
    message.warning('请输入要保存的文案内容');
    return;
  }

  if (!currentEditingCopywriting.value?.id) {
    message.error('缺少必要的保存信息');
    return;
  }

  try {
    const params = {
      content: copywritingEditForm.value.content,
      id: currentEditingCopywriting.value.id,
    };

    await updateCopywriting(params);
    message.success('文案保存成功');

    // 更新当前项的内容
    if (currentEditingCopywriting.value) {
      currentEditingCopywriting.value.answer =
        copywritingEditForm.value.content;
    }

    // 刷新列表
    await loadCopywritingList();

    closeCopywritingEditModal();
  } catch (error) {
    console.error('文案保存失败:', error);
    message.error('文案保存失败，请重试');
  }
};

// 分页处理方法
const handleMaterialPageChange = (page: number, pageSize: number) => {
  materialPagination.value.current = page;
  materialPagination.value.pageSize = pageSize;
  loadMaterialList();
};

const handleTitlePageChange = (page: number, pageSize: number) => {
  titlePagination.value.current = page;
  titlePagination.value.pageSize = pageSize;
  loadTitleList();
};

const handleCopywritingPageChange = (page: number, pageSize: number) => {
  copywritingPagination.value.current = page;
  copywritingPagination.value.pageSize = pageSize;
  loadCopywritingList();
};

// 搜索POI方法
const searchPoi = async () => {
  if (!formData.value.merchantName || formData.value.city.length === 0) {
    return;
  }

  try {
    poiLoading.value = true;
    const cityCode = formData.value.city[1]; // 使用市级的城市code（第二级）

    if (!cityCode) {
      console.error('城市代码不能为空');
      return;
    }

    const params = {
      keyword: formData.value.merchantName,
      city: cityCode,
    };

    const response = await searchPoiList(params);
    poiOptions.value = response.map((poi) => ({
      value: poi.poi_id,
      label: poi.poi_name,
    }));

    // console.log('POI搜索结果:', response);
  } catch (error) {
    console.error('POI搜索失败:', error);
    poiOptions.value = [];
  } finally {
    poiLoading.value = false;
  }
};

// 加载账户分组
const loadAccountGroups = async () => {
  if (!selectedPlatform.value) {
    groupOptions.value = [];
    return;
  }

  try {
    groupLoading.value = true;
    const params = {
      uid: 7,
      type: selectedPlatform.value,
    };

    const response = await getAccountGroupList(params);
    groupOptions.value = response.map((group) => ({
      value: group.id,
      label: group.name,
    }));

    // 清空之前选择的分组和账户
    formData.value.accountGroup = undefined;
    formData.value.selectedAccounts = [];
    accountOptions.value = [];
  } catch (error) {
    console.error('加载账户分组失败:', error);
    groupOptions.value = [];
  } finally {
    groupLoading.value = false;
  }
};

// 日期处理工具函数
const msToDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return {
    hasTime: `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`,
  };
};

const formatDate = (dateStr: string) => {
  return dateStr;
};

// 账户失效判断逻辑
const isAccountExpired = (expiresIn: string) => {
  let isEmpower = false;
  // true,已授权 ,为false，则为已失效
  isEmpower =
    new Date(msToDate(new Date()).hasTime.replaceAll('-', '/')) <
    new Date(formatDate(expiresIn).replaceAll('-', '/'));
  return !isEmpower; // 返回是否失效（true表示失效）
};

// 加载分组下的账户
const loadAccounts = async (groupId: number) => {
  if (!groupId || !selectedPlatform.value) {
    accountOptions.value = [];
    return;
  }

  try {
    accountLoading.value = true;
    const params = {
      account_group_id: groupId,
      page: 1,
      psize: 200,
      type: selectedPlatform.value,
      uid: 7,
    };

    const response = await getAccountList(params);

    // 保存原始账户数据
    rawAccountData.value = response.list;

    // 处理用于显示的账户选项
    accountOptions.value = response.list.map((account) => {
      const isExpired = isAccountExpired(account.expires_in);
      return {
        value: account.id,
        label: account.account_name,
        avatar: account.avatar,
        disabled: isExpired,
        expired: isExpired,
        expiresIn: account.expires_in,
      };
    });

    // 清空之前选择的账户
    formData.value.selectedAccounts = [];
  } catch (error) {
    console.error('加载账户列表失败:', error);
    accountOptions.value = [];
  } finally {
    accountLoading.value = false;
  }
};

// 计算属性
const isMaterialDisabled = computed(() => {
  // 只有当前已选择的素材视频数量为0时，才禁用按钮显示
  // 允许用户打开抽屉查看所有素材，在确认选择时再进行检查
  return formData.value.selectedMaterialId && formData.value.videoCount === 0;
});

// 动态验证规则 - 根据平台和挂载类型判断字段是否必填
const dynamicFormRules = computed(() => {
  const rules = { ...formRules };

  // 抖音平台：如果选择了商家POI，则相关字段必填
  if (selectedPlatform.value === 1 && formData.value.mountType === '商家POI') {
    rules.city = [{ required: true, message: '请选择城市', trigger: 'change' }];
    rules.merchantName = [
      { required: true, message: '请输入商家简称', trigger: 'blur' },
    ];
    rules.poiAddress = [
      { required: true, message: '请选择POI地址', trigger: 'change' },
    ];
  }

  // 快手平台：如果选择了小程序，则相关字段必填
  if (selectedPlatform.value === 2 && formData.value.mountType === '小程序') {
    rules.appId = [
      { required: true, message: '请输入快手小程序APPID', trigger: 'blur' },
    ];
    rules.miniProgramName = [
      { required: true, message: '请输入快手小程序名称', trigger: 'blur' },
    ];
    rules.miniProgramUrl = [
      { required: true, message: '请输入快手小程序URL', trigger: 'blur' },
      { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' },
    ];
  }

  return rules;
});

// 检查第一步是否完成
const isFirstStepComplete = computed(() => {
  // 第一步只检查基础必填字段（不包括账户相关）
  // console.log('验证第一步完成状态:', {
  //   taskName: formData.value.taskName,
  //   selectedMaterial: formData.value.selectedMaterial,
  //   selectedTitlesLength: formData.value.selectedTitles.length,
  //   platform: selectedPlatform.value,
  //   mountType: formData.value.mountType,
  // });

  // 基础必填字段检查（任务名称、素材、标题）
  if (!formData.value.taskName || !formData.value.selectedMaterial) {
    // console.log('基础必填字段验证失败');
    return false;
  }

  // 标题验证：根据平台类型检查不同的标题字段
  if (selectedPlatform.value === 3) {
    // 视频号：检查titleList
    const hasValidTitle = formData.value.titleList.some(
      (item) => item.title && item.title.trim(),
    );
    if (!hasValidTitle) {
      return false;
    }
  } else {
    // 其他平台：检查selectedTitles
    if (formData.value.selectedTitles.length === 0) {
      return false;
    }
  }

  // 抖音平台特有字段检查
  if (
    selectedPlatform.value === 1 &&
    formData.value.mountType === '商家POI' &&
    (formData.value.city.length === 0 ||
      !formData.value.merchantName ||
      !formData.value.poiAddress)
  ) {
    return false;
  }

  // 快手平台特有字段检查
  if (
    selectedPlatform.value === 2 &&
    formData.value.mountType === '小程序' &&
    (!formData.value.appId ||
      !formData.value.miniProgramName ||
      !formData.value.miniProgramUrl ||
      !formData.value.cartId)
  ) {
    return false;
  }

  // 小红书平台特有字段检查
  if (selectedPlatform.value === 4 && !formData.value.copywiring) {
    return false;
  }

  return true;
});

// 时间选择相关方法
const initAccountTimeSelections = () => {
  const selections: Record<number, Record<string, string[]>> = {};

  formData.value.selectedAccounts.forEach((accountId) => {
    // 如果账户已存在，保留其已选择的时间
    if (accountTimeSelections.value[accountId]) {
      selections[accountId] = {};
      // 深拷贝已存在的时间选择
      availableDays.value.forEach((day) => {
        selections[accountId][day.date] = [
          ...(accountTimeSelections.value[accountId][day.date] || []),
        ];
      });
    } else {
      // 新账户初始化空的时间选择
      selections[accountId] = {};
      availableDays.value.forEach((day) => {
        selections[accountId][day.date] = [];
      });
    }
  });

  // 只保留当前选中账户的时间选择，移除未选中账户的数据
  accountTimeSelections.value = selections;
};

// 切换时间选择（在弹窗中操作临时状态）
const toggleTimeSelection = (accountId: number, date: string, time: string) => {
  // 在弹窗中操作临时状态
  if (!tempTimeSelections.value[date]) {
    tempTimeSelections.value[date] = [];
  }

  const timeIndex = tempTimeSelections.value[date].indexOf(time);
  if (timeIndex === -1) {
    tempTimeSelections.value[date].push(time);
  } else {
    tempTimeSelections.value[date].splice(timeIndex, 1);
  }
};

// 全选当前日期的所有时间（在弹窗中操作临时状态）
const selectAllTimesForDate = (accountId: number, date: string) => {
  if (!tempTimeSelections.value[date]) {
    tempTimeSelections.value[date] = [];
  }

  const currentTimes = tempTimeSelections.value[date] || [];
  // 根据当前选择状态决定全选或取消全选
  tempTimeSelections.value[date] =
    currentTimes.length === dayData.length
      ? [] // 如果已全选，则取消全选
      : [...dayData]; // 否则全选
};

// 检查时间是否被选中（在弹窗中检查临时状态）
const isTimeSelected = (_accountId: number, date: string, time: string) => {
  return tempTimeSelections.value[date]?.includes(time) || false;
};

// 检查日期是否全选（在弹窗中检查临时状态）
const isDateFullySelected = (_accountId: number, date: string) => {
  const selectedTimes = tempTimeSelections.value[date] || [];
  return selectedTimes.length === dayData.length;
};

// 获取账户名称
const getAccountName = (accountId: number) => {
  const account = accountOptions.value.find((acc) => acc.value === accountId);
  return account ? account.label : `账户${accountId}`;
};

// 获取账户信息
const getAccountInfo = (accountId: number) => {
  const account = accountOptions.value.find((acc) => acc.value === accountId);
  return account || null;
};

// 打开时间选择弹窗
const openTimeSelectionModal = (accountId: number) => {
  currentEditingAccount.value = accountId;

  // 初始化临时选择状态，复制当前账户的已选时间
  const currentSelections = accountTimeSelections.value[accountId] || {};
  tempTimeSelections.value = {};
  availableDays.value.forEach((day) => {
    tempTimeSelections.value[day.date] = [
      ...(currentSelections[day.date] || []),
    ];
  });

  timeSelectionModalVisible.value = true;
};

// 关闭时间选择弹窗
const closeTimeSelectionModal = () => {
  timeSelectionModalVisible.value = false;
  currentEditingAccount.value = null;
  tempTimeSelections.value = {};
};

// 确认时间选择
const confirmTimeSelection = () => {
  if (currentEditingAccount.value) {
    // 将临时选择保存到正式状态
    if (!accountTimeSelections.value[currentEditingAccount.value]) {
      accountTimeSelections.value[currentEditingAccount.value] = {};
    }

    Object.keys(tempTimeSelections.value).forEach((date) => {
      accountTimeSelections.value[currentEditingAccount.value!][date] = [
        ...tempTimeSelections.value[date],
      ];
    });
  }

  closeTimeSelectionModal();
};

// 检查账户是否已选择时间
const hasAccountSelectedTime = (accountId: number) => {
  const selections = accountTimeSelections.value[accountId];
  if (!selections) return false;

  return Object.values(selections).some((times) => times.length > 0);
};

// 获取账户已选时间数量
const getAccountSelectedTimeCount = (accountId: number) => {
  const selections = accountTimeSelections.value[accountId];
  if (!selections) return 0;

  return Object.values(selections).reduce(
    (total, times) => total + times.length,
    0,
  );
};

// 禁用开始发布日期的逻辑（只能选择明天及以后的日期）
const disabledStartDate = (current: any) => {
  // 获取明天的日期（当前日期 + 1天）
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0); // 设置为当天的开始时间

  // 禁用明天之前的所有日期
  return current && current.valueOf() < tomorrow.valueOf();
};

// 生成日期数组
const getDateList = (startDate: string, days: number) => {
  const dates = [];
  const start = new Date(startDate);
  for (let i = 0; i < days; i++) {
    const date = new Date(start);
    date.setDate(start.getDate() + i);
    dates.push(date.toISOString().split('T')[0]);
  }
  return dates;
};

// 随机选择时间
const randomSelectTimes = (timeArray: string[], count: number) => {
  const shuffled = [...timeArray].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// 生成随机发布任务数组
const generateRandomTasks = () => {
  const scheduleTimes: any[] = [];
  const dateList = getDateList(
    formData.value.startDate!,
    formData.value.publishDays,
  );

  // 为每个账户生成完整的任务数据
  formData.value.selectedAccounts.forEach((accountId) => {
    // 从原始账户数据中查找账户
    const rawAccount = rawAccountData.value.find((acc) => acc.id === accountId);
    if (!rawAccount) return;

    dateList.forEach((date) => {
      const randomTimes = randomSelectTimes(dayData, formData.value.dailyCount);
      randomTimes.forEach((time) => {
        // 使用原始账户的完整数据，只添加三个额外字段
        const taskItem = {
          // 账户列表的所有原始数据字段
          ...rawAccount, // 展开原始账户的所有字段

          // 只添加三个额外字段
          dyAccountId: rawAccount.id, // 账户ID
          productPolderId: formData.value.selectedMaterialId, // 素材ID
          publishTime: `${date} ${time}:00`, // 发布时间
        };

        scheduleTimes.push(taskItem);
      });
    });
  });

  return scheduleTimes;
};

// 生成指定时间发布任务数组
const generateSpecificTasks = () => {
  const scheduleTimes: any[] = [];

  formData.value.selectedAccounts.forEach((accountId) => {
    // 从原始账户数据中查找账户
    const rawAccount = rawAccountData.value.find((acc) => acc.id === accountId);
    if (!rawAccount) return;

    const accountSelections = accountTimeSelections.value[accountId];
    if (!accountSelections) return;

    Object.keys(accountSelections).forEach((date) => {
      const times = accountSelections[date];
      times.forEach((time) => {
        // 使用原始账户的完整数据，只添加三个额外字段
        const taskItem = {
          // 账户列表的所有原始数据字段
          ...rawAccount, // 展开原始账户的所有字段

          // 只添加三个额外字段
          dyAccountId: rawAccount.id, // 账户ID
          productPolderId: formData.value.selectedMaterialId, // 素材ID
          publishTime: `${date} ${time}:00`, // 发布时间
        };

        scheduleTimes.push(taskItem);
      });
    });
  });

  return scheduleTimes;
};

// 验证任务数量是否超过视频数量
const validateTaskCount = (tasks: any[]) => {
  const videoCount = formData.value.videoCount;
  if (tasks.length > videoCount) {
    message.warning(`超过最大视频发布数(${videoCount})，请重新设置发布时间`);
    return false;
  }
  return true;
};

// 监听平台选择变化，自动加载分组
watch(selectedPlatform, (newPlatform) => {
  if (newPlatform) {
    loadAccountGroups(newPlatform);
  }
});

// 监听分组选择变化，自动加载账户
watch(
  () => formData.value.accountGroup,
  (newGroupId) => {
    if (newGroupId && selectedPlatform.value) {
      loadAccounts(Number(newGroupId), selectedPlatform.value);
    }
  },
);

// 监听账户选择变化，自动初始化时间选择
watch(
  () => formData.value.selectedAccounts,
  () => {
    initAccountTimeSelections();
  },
  { deep: true },
);

onMounted(() => {
  // 页面初始化
  loadStatistics();
  loadTaskList();
});
</script>

<template>
  <Page class="matrix-dashboard">
    <!-- 顶部主要内容区域 -->
    <div class="main-content-section">
      <ARow :gutter="[24, 24]">
        <!-- 左侧统计数据卡片 -->
        <ACol :xs="24" :lg="14">
          <div class="stats-card-container">
            <div class="stats-card-header">
              <h2 class="stats-title">累计发布账户</h2>
              <div class="stats-main-value">
                {{ formatNumber(statsData.account_count) }}
              </div>
            </div>
            <div class="stats-grid">
              <div class="stats-item">
                <div class="stats-platform">D抖音账户</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.douyin_account_count) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">K快手账户</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.kuaishou_account_count) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">视频号账户</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.shipinghao_account_count) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">小红薯账户</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.xiaohong_account_count) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">累计曝光</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.exposure_count_total) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">累计点赞</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.like_count_total) }}
                </div>
              </div>
              <div class="stats-item">
                <div class="stats-platform">累计评价</div>
                <div class="stats-value">
                  {{ formatNumber(statsData.comment_total) }}
                </div>
              </div>
            </div>
          </div>
        </ACol>
        <!-- 右侧功能卡片 -->
        <ACol :xs="24" :lg="10">
          <div class="modules-grid">
            <ACard
              v-for="module in moduleData"
              :key="module.key"
              class="module-card"
              hoverable
              @click="handleModuleClick(module)"
            >
              <div class="module-content">
                <div class="module-icon">{{ module.icon }}</div>
                <div class="module-info">
                  <h3 class="module-title">{{ module.title }}</h3>
                  <p class="module-description">{{ module.description }}</p>
                </div>
              </div>
            </ACard>
          </div>
        </ACol>
      </ARow>
    </div>

    <!-- 任务卡片区域 -->
    <div class="tasks-section">
      <!-- 平台筛选标签页 -->
      <ATabs
        v-model:active-key="activeTaskTab"
        @change="handleTaskTabChange"
        class="task-tabs"
      >
        <ATabPane
          v-for="tab in taskTabsData"
          :key="tab.key"
          :tab="`${tab.label}任务`"
        />
      </ATabs>

      <div class="task-cards-container">
        <TaskCard
          v-for="task in taskList"
          :key="task.id"
          :task="task"
          @action="handleTaskAction"
        />
      </div>

      <!-- 任务分页 -->
      <div class="task-pagination-wrapper">
        <APagination
          v-model:current="taskPagination.current"
          v-model:page-size="taskPagination.pageSize"
          :total="taskPagination.total"
          :show-size-changer="taskPagination.showSizeChanger"
          :show-quick-jumper="taskPagination.showQuickJumper"
          :show-total="taskPagination.showTotal"
          @change="handleTaskPageChange"
        />
      </div>
    </div>

    <!-- 平台选择对话框 -->
    <AModal
      v-model:visible="platformSelectVisible"
      title="选择发布平台"
      :footer="null"
      width="500px"
      centered
    >
      <div class="platform-select-grid">
        <div
          v-for="platform in platformOptions"
          :key="platform.value"
          class="platform-option"
          @click="handlePlatformSelect(platform.value)"
        >
          <div class="platform-icon">{{ platform.icon }}</div>
          <div class="platform-name">{{ platform.label }}</div>
        </div>
      </div>
    </AModal>

    <!-- 多步骤发布表单 -->
    <AModal
      v-model:visible="publishModalVisible"
      :title="`发布视频 - ${platformOptions.find((p) => p.value === selectedPlatform)?.label || ''}`"
      width="800px"
      centered
      :footer="null"
      :mask-closable="false"
    >
      <ASteps :current="currentStep" class="publish-steps">
        <AStep title="基础信息" />
        <AStep title="发布设置" />
      </ASteps>

      <div class="step-content">
        <!-- 第一步：基础信息 -->
        <div v-if="currentStep === 0" class="step-form">
          <AForm :model="formData" :rules="dynamicFormRules" layout="vertical">
            <AFormItem label="任务名称" name="taskName" required>
              <AInput
                v-model:value="formData.taskName"
                placeholder="请输入任务名称"
                :maxlength="30"
                show-count
              />
            </AFormItem>

            <ARow :gutter="16">
              <ACol :span="12">
                <AFormItem label="选择素材" name="selectedMaterial" required>
                  <AButton
                    @click="openMaterialDrawer"
                    block
                    :disabled="isMaterialDisabled"
                  >
                    <template v-if="isMaterialDisabled">
                      <span style="color: #ff4d4f"
                        >{{ formData.selectedMaterial }}（视频数量为0）</span
                      >
                    </template>
                    <template v-else>
                      {{ formData.selectedMaterial || '选择素材' }}
                    </template>
                  </AButton>
                </AFormItem>
              </ACol>
              <ACol :span="12">
                <!-- 视频号平台：标题输入 -->
                <AFormItem
                  v-if="selectedPlatform === 3"
                  label="输入标题"
                  name="titleList"
                  required
                >
                  <div class="title-input-container">
                    <div
                      v-for="(item, index) in formData.titleList"
                      :key="index"
                      class="title-input-item"
                    >
                      <AInput
                        v-model:value="item.title"
                        :placeholder="`标题${index + 1}（最多10个字符）`"
                        :maxlength="10"
                        show-count
                        @input="validateTitleList"
                      />
                      <AButton
                        v-if="formData.titleList.length > 1"
                        type="text"
                        danger
                        size="small"
                        @click="removeTitleItem(index)"
                      >
                        删除
                      </AButton>
                    </div>
                    <AButton
                      type="dashed"
                      block
                      @click="addTitleItem"
                      :disabled="formData.titleList.length >= 10"
                    >
                      + 添加标题
                    </AButton>
                  </div>
                </AFormItem>

                <!-- 其他平台：标题选择 -->
                <AFormItem
                  v-else
                  label="选择标题"
                  name="selectedTitles"
                  required
                >
                  <AButton @click="openTitleDrawer" block>
                    {{
                      formData.selectedTitles.length > 0
                        ? `已选${formData.selectedTitles.length}条标题`
                        : '选择标题'
                    }}
                  </AButton>
                </AFormItem>
              </ACol>
            </ARow>

            <!-- 显示视频数量（仅在选择了素材后显示） -->
            <ARow v-if="formData.selectedMaterial" :gutter="16">
              <ACol :span="12">
                <AFormItem label="视频数量">
                  <AInput
                    :value="formData.videoCount"
                    readonly
                    placeholder="视频数量"
                    suffix="个"
                  />
                </AFormItem>
              </ACol>
            </ARow>

            <!-- 抖音平台特有字段 -->
            <template v-if="selectedPlatform === 1">
              <AFormItem label="挂载类型">
                <ARadioGroup v-model:value="formData.mountType">
                  <ARadio value="不挂载">不挂载</ARadio>
                  <ARadio value="商家POI">商家POI</ARadio>
                </ARadioGroup>
              </AFormItem>

              <template v-if="formData.mountType === '商家POI'">
                <AFormItem
                  label="选择城市"
                  name="city"
                  :required="
                    selectedPlatform === 1 && formData.mountType === '商家POI'
                  "
                >
                  <ACascader
                    v-model:value="formData.city"
                    :options="cityOptions"
                    placeholder="请选择城市"
                  />
                </AFormItem>

                <AFormItem
                  label="商家简称"
                  name="merchantName"
                  :required="
                    selectedPlatform === 1 && formData.mountType === '商家POI'
                  "
                >
                  <AInput
                    v-model:value="formData.merchantName"
                    placeholder="请输入商家简称"
                  >
                    <template #addonAfter>
                      <AButton
                        @click="searchPoi"
                        size="small"
                        :loading="poiLoading"
                        :disabled="
                          !formData.merchantName || formData.city.length === 0
                        "
                      >
                        搜索
                      </AButton>
                    </template>
                  </AInput>
                </AFormItem>

                <AFormItem
                  label="POI地址"
                  name="poiAddress"
                  :required="
                    selectedPlatform === 1 && formData.mountType === '商家POI'
                  "
                >
                  <ASelect
                    v-model:value="formData.poiAddress"
                    placeholder="请选择POI地址"
                    :loading="poiLoading"
                    :disabled="poiOptions.length === 0"
                  >
                    <ASelectOption
                      v-for="poi in poiOptions"
                      :key="poi.value"
                      :value="poi.value"
                    >
                      {{ poi.label }}
                    </ASelectOption>
                  </ASelect>
                </AFormItem>
              </template>
            </template>

            <!-- 快手平台特有字段 -->
            <template v-if="selectedPlatform === 2">
              <AFormItem label="挂载类型">
                <ARadioGroup v-model:value="formData.mountType">
                  <ARadio value="不挂载">不挂载</ARadio>
                  <ARadio value="小程序">小程序</ARadio>
                </ARadioGroup>
              </AFormItem>

              <template v-if="formData.mountType === '小程序'">
                <AFormItem
                  label="快手小程序APPID"
                  name="appId"
                  :required="
                    selectedPlatform === 2 && formData.mountType === '小程序'
                  "
                >
                  <AInput
                    v-model:value="formData.appId"
                    placeholder="请输入快手小程序APPID"
                  />
                </AFormItem>

                <AFormItem
                  label="快手小程序名称"
                  name="miniProgramName"
                  :required="
                    selectedPlatform === 2 && formData.mountType === '小程序'
                  "
                >
                  <AInput
                    v-model:value="formData.miniProgramName"
                    placeholder="请输入快手小程序名称"
                  />
                </AFormItem>

                <AFormItem
                  label="快手小程序URL"
                  name="miniProgramUrl"
                  :required="
                    selectedPlatform === 2 && formData.mountType === '小程序'
                  "
                >
                  <AInput
                    v-model:value="formData.miniProgramUrl"
                    placeholder="请输入快手小程序URL"
                  />
                </AFormItem>

                <AFormItem
                  label="小黄车ID"
                  :required="
                    selectedPlatform === 2 && formData.mountType === '小程序'
                  "
                >
                  <AInput
                    v-model:value="formData.cartId"
                    placeholder="请输入小黄车ID"
                  />
                </AFormItem>
              </template>
            </template>

            <!-- 小红书平台特有字段 -->
            <template v-if="selectedPlatform === 4">
              <AFormItem label="选择文案" required>
                <AButton @click="openCopywritingDrawer" block>
                  {{
                    selectedCopywriting ? selectedCopywriting.name : '选择文案'
                  }}
                </AButton>
              </AFormItem>

              <AFormItem label="商品ID" name="productId">
                <AInput
                  v-model:value="formData.productId"
                  placeholder="请输入商品ID"
                />
              </AFormItem>
            </template>
          </AForm>
        </div>

        <!-- 第二步：发布设置 -->
        <div v-if="currentStep === 1" class="step-form">
          <AForm :model="formData" :rules="formRules" layout="vertical">
            <AFormItem label="发布时间类型" name="publishType" required>
              <ARadioGroup v-model:value="formData.publishType">
                <ARadio value="随机发布">随机发布</ARadio>
                <ARadio value="指定时间发布">指定时间发布</ARadio>
              </ARadioGroup>
            </AFormItem>

            <!-- 随机发布选项 -->
            <template v-if="formData.publishType === '随机发布'">
              <AFormItem label="开始发布日期" name="startDate" required>
                <ADatePicker
                  v-model:value="formData.startDate"
                  placeholder="请选择开始发布日期"
                  style="width: 100%"
                  :disabled-date="disabledStartDate"
                />
              </AFormItem>

              <ARow :gutter="16">
                <ACol :span="12">
                  <AFormItem label="每天发布数量" name="dailyCount" required>
                    <AInputNumber
                      v-model:value="formData.dailyCount"
                      :min="1"
                      :max="35"
                      style="width: 100%"
                    />
                  </AFormItem>
                </ACol>
                <ACol :span="12">
                  <AFormItem label="发布天数" name="publishDays" required>
                    <AInputNumber
                      v-model:value="formData.publishDays"
                      :min="1"
                      style="width: 100%"
                    />
                  </AFormItem>
                </ACol>
              </ARow>
            </template>

            <!-- 指定时间发布选项 -->
            <template v-if="formData.publishType === '指定时间发布'">
              <div class="time-selection-container">
                <h4>为每个账户选择发布时间</h4>
                <div class="account-cards-container">
                  <div
                    v-for="accountId in formData.selectedAccounts"
                    :key="accountId"
                    class="account-card"
                    @click="openTimeSelectionModal(accountId)"
                  >
                    <div class="account-info">
                      <AAvatar
                        :size="48"
                        :src="getAccountInfo(accountId)?.avatar"
                        class="account-avatar"
                      >
                        {{ getAccountName(accountId).charAt(0) }}
                      </AAvatar>
                      <div class="account-details">
                        <div class="account-name">
                          {{ getAccountName(accountId) }}
                        </div>
                        <div class="account-status">
                          <span
                            v-if="hasAccountSelectedTime(accountId)"
                            class="time-selected"
                          >
                            时间已选 ({{
                              getAccountSelectedTimeCount(accountId)
                            }}个)
                          </span>
                          <span v-else class="time-unselected">
                            点击选择时间
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 账户选择（两种发布类型都需要） -->
            <AFormItem label="账户分组" name="accountGroup" required>
              <ASelect
                v-model:value="formData.accountGroup"
                placeholder="请选择账户分组"
                :loading="groupLoading"
                :disabled="!selectedPlatform"
              >
                <ASelectOption
                  v-for="group in groupOptions"
                  :key="group.value"
                  :value="group.value"
                >
                  {{ group.label }}
                </ASelectOption>
              </ASelect>
            </AFormItem>

            <AFormItem label="选择账户" name="selectedAccounts" required>
              <ASelect
                v-model:value="formData.selectedAccounts"
                mode="multiple"
                placeholder="请选择账户"
                :loading="accountLoading"
                :disabled="!formData.accountGroup"
              >
                <ASelectOption
                  v-for="account in accountOptions"
                  :key="account.value"
                  :value="account.value"
                  :disabled="account.disabled"
                >
                  <span :class="{ 'expired-account': account.expired }">
                    {{ account.label }}
                    <span v-if="account.expired" class="expired-tag"
                      >(已失效)</span
                    >
                  </span>
                </ASelectOption>
              </ASelect>
            </AFormItem>
          </AForm>
        </div>
      </div>

      <!-- 步骤导航按钮 -->
      <div class="step-actions">
        <AButton v-if="currentStep > 0" @click="handlePrevStep">
          上一步
        </AButton>
        <AButton
          v-if="currentStep < 1"
          type="primary"
          @click="handleNextStep"
          :disabled="!isFirstStepComplete"
        >
          下一步
        </AButton>
        <AButton v-if="currentStep === 1" type="primary" @click="handleSubmit">
          提交
        </AButton>
        <AButton @click="handleCancel" style="margin-left: 8px"> 取消 </AButton>
      </div>
    </AModal>

    <!-- 素材选择抽屉 -->
    <ADrawer
      v-model:visible="materialDrawerVisible"
      title="选择素材"
      placement="right"
      width="600px"
      @close="closeMaterialDrawer"
    >
      <div class="drawer-content">
        <div v-if="materialLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else>
          <div class="material-list">
            <ARadioGroup
              v-model:value="selectedMaterial"
              class="material-radio-group"
            >
              <div
                v-for="material in materialList"
                :key="material.id"
                class="material-item"
                :class="{ 'material-disabled': material.video_count === 0 }"
              >
                <ARadio
                  :value="material"
                  class="material-radio"
                  :disabled="material.video_count === 0"
                >
                  <div class="material-content">
                    <div class="material-title">
                      <span class="material-title-text">
                        {{ material.title }}
                      </span>
                      <span
                        v-if="material.video_count === 0"
                        class="disabled-tag"
                        >（不可选择）</span
                      >
                    </div>
                    <div class="material-info">
                      <span class="material-time">{{
                        material.create_time
                      }}</span>
                      <span
                        class="material-count"
                        :class="{ 'count-zero': material.video_count === 0 }"
                      >
                        视频数量: {{ material.video_count }}
                      </span>
                    </div>
                    <!-- 查看视频按钮 -->
                    <div class="material-actions">
                      <AButton
                        v-if="material.status === 4"
                        type="primary"
                        size="small"
                        @click.stop="openProductVideoModal(material)"
                      >
                        查看视频
                      </AButton>
                      <AButton v-else type="default" size="small" disabled>
                        查看视频
                      </AButton>
                    </div>
                  </div>
                </ARadio>
              </div>
            </ARadioGroup>
          </div>

          <div class="drawer-pagination">
            <APagination
              v-model:current="materialPagination.current"
              v-model:page-size="materialPagination.pageSize"
              :total="materialPagination.total"
              :show-size-changer="false"
              :show-quick-jumper="false"
              size="small"
              @change="handleMaterialPageChange"
            />
          </div>

          <div class="drawer-actions">
            <AButton
              type="primary"
              @click="handleMaterialConfirm"
              :disabled="!selectedMaterial"
            >
              确认选择
            </AButton>
          </div>
        </div>
      </div>
    </ADrawer>

    <!-- 标题选择抽屉 -->
    <ADrawer
      v-model:visible="titleDrawerVisible"
      title="选择标题"
      placement="right"
      width="700px"
      @close="closeTitleDrawer"
    >
      <div class="drawer-content">
        <div v-if="titleLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else>
          <div class="title-list">
            <ACheckboxGroup
              v-model:value="selectedTitleIds"
              class="title-checkbox-group"
            >
              <div
                v-for="title in titleList"
                :key="title.id"
                class="title-item"
              >
                <ACheckbox :value="title.id" class="title-checkbox">
                  <div class="title-content">
                    <div class="title-text">
                      {{ title.answer }}{{ title.challenges }}
                    </div>
                    <div class="title-footer">
                      <div class="title-time">{{ title.create_time }}</div>
                      <AButton
                        size="small"
                        type="text"
                        class="title-edit-btn"
                        @click.stop="openTitleEditModal(title)"
                      >
                        编辑
                      </AButton>
                    </div>
                  </div>
                </ACheckbox>
              </div>
            </ACheckboxGroup>
          </div>

          <div class="drawer-pagination">
            <APagination
              v-model:current="titlePagination.current"
              v-model:page-size="titlePagination.pageSize"
              :total="titlePagination.total"
              :show-size-changer="false"
              :show-quick-jumper="false"
              size="small"
              @change="handleTitlePageChange"
            />
          </div>

          <div class="drawer-actions">
            <AButton type="primary" @click="handleTitleConfirm">
              确认选择 ({{ selectedTitleIds.length }})
            </AButton>
          </div>
        </div>
      </div>
    </ADrawer>

    <!-- 文案选择抽屉 -->
    <ADrawer
      v-model:open="copywritingDrawerVisible"
      title="选择文案"
      placement="right"
      width="600"
    >
      <div class="drawer-content">
        <div v-if="copywritingLoading" class="loading-container">
          <div class="loading-text">加载中...</div>
        </div>
        <div v-else>
          <div class="copywriting-list">
            <ARadioGroup
              v-model:value="selectedCopywriting"
              class="copywriting-radio-group"
            >
              <div
                v-for="copywriting in copywritingList"
                :key="copywriting.id"
                class="copywriting-item"
              >
                <ARadio :value="copywriting" class="copywriting-radio">
                  <div class="copywriting-content">
                    <div class="copywriting-header">
                      <div class="copywriting-title">
                        {{ copywriting.name }}
                      </div>
                      <div class="copywriting-header-right">
                        <span class="copywriting-time">{{
                          copywriting.create_time
                        }}</span>
                        <AButton
                          size="small"
                          type="text"
                          class="copywriting-edit-btn"
                          @click.stop="openCopywritingEditModal(copywriting)"
                        >
                          编辑
                        </AButton>
                      </div>
                    </div>
                    <div class="copywriting-answer">
                      {{ copywriting.answer }}
                    </div>
                  </div>
                </ARadio>
              </div>
            </ARadioGroup>
          </div>
        </div>

        <div class="drawer-pagination">
          <APagination
            v-model:current="copywritingPagination.current"
            v-model:page-size="copywritingPagination.pageSize"
            :total="copywritingPagination.total"
            :show-size-changer="false"
            :show-quick-jumper="false"
            size="small"
            @change="handleCopywritingPageChange"
          />
        </div>

        <div class="drawer-actions">
          <AButton
            type="primary"
            @click="handleCopywritingConfirm"
            :disabled="!selectedCopywriting"
          >
            确认选择
          </AButton>
        </div>
      </div>
    </ADrawer>

    <!-- 标题编辑弹窗 -->
    <AModal
      v-model:open="titleEditModalVisible"
      title="编辑标题"
      :width="600"
      centered
      @cancel="closeTitleEditModal"
      class="title-edit-modal"
    >
      <div class="edit-form-content">
        <AForm layout="vertical">
          <AFormItem label="标题" required>
            <AInput
              v-model:value="titleEditForm.answer"
              placeholder="请输入标题内容"
              :maxlength="100"
              show-count
            />
            <div class="form-actions">
              <AButton
                type="default"
                size="small"
                :loading="titleAnswerRewriteLoading"
                @click="handleTitleAnswerRewrite"
              >
                AI改写
              </AButton>
            </div>
          </AFormItem>

          <AFormItem label="话题" required>
            <AInput
              v-model:value="titleEditForm.challenges"
              placeholder="请输入话题内容"
              :maxlength="100"
              show-count
            />
            <div class="form-actions">
              <AButton
                type="default"
                size="small"
                :loading="titleChallengesRewriteLoading"
                @click="handleTitleChallengesRewrite"
              >
                AI改写
              </AButton>
            </div>
          </AFormItem>
        </AForm>
      </div>

      <template #footer>
        <div class="edit-modal-footer">
          <AButton @click="closeTitleEditModal">取消</AButton>
          <AButton
            type="primary"
            :disabled="
              titleAnswerRewriteLoading || titleChallengesRewriteLoading
            "
            @click="handleTitleSave"
          >
            保存
          </AButton>
        </div>
      </template>
    </AModal>

    <!-- 文案编辑弹窗 -->
    <AModal
      v-model:open="copywritingEditModalVisible"
      title="编辑文案"
      :width="600"
      centered
      @cancel="closeCopywritingEditModal"
      class="copywriting-edit-modal"
    >
      <div class="edit-form-content">
        <AForm layout="vertical">
          <AFormItem label="文案内容" required>
            <AInput.TextArea
              v-model:value="copywritingEditForm.content"
              placeholder="请输入文案内容"
              :rows="6"
              :maxlength="500"
              show-count
            />
            <div class="form-actions">
              <AButton
                type="default"
                size="small"
                :loading="copywritingRewriteLoading"
                @click="handleCopywritingRewrite"
              >
                AI改写
              </AButton>
            </div>
          </AFormItem>
        </AForm>
      </div>

      <template #footer>
        <div class="edit-modal-footer">
          <AButton @click="closeCopywritingEditModal">取消</AButton>
          <AButton
            type="primary"
            :disabled="copywritingRewriteLoading"
            @click="handleCopywritingSave"
          >
            保存
          </AButton>
        </div>
      </template>
    </AModal>

    <!-- 时间选择弹窗 -->
    <AModal
      v-model:open="timeSelectionModalVisible"
      title="选择发布时间"
      width="800px"
      :footer="null"
    >
      <div v-if="currentEditingAccount" class="time-selection-modal">
        <div class="modal-account-info">
          <AAvatar
            :size="40"
            :src="getAccountInfo(currentEditingAccount)?.avatar"
          >
            {{ getAccountName(currentEditingAccount).charAt(0) }}
          </AAvatar>
          <span class="modal-account-name">{{
            getAccountName(currentEditingAccount)
          }}</span>
        </div>

        <div class="time-selection-content">
          <div v-for="day in availableDays" :key="day.date" class="day-section">
            <div class="day-header">
              <span class="day-label">{{ day.label }}</span>
              <AButton
                size="small"
                @click="selectAllTimesForDate(currentEditingAccount, day.date)"
                :type="
                  isDateFullySelected(currentEditingAccount, day.date)
                    ? 'primary'
                    : 'default'
                "
              >
                {{
                  isDateFullySelected(currentEditingAccount, day.date)
                    ? '取消全选'
                    : '全选'
                }}
              </AButton>
            </div>

            <div class="time-grid">
              <AButton
                v-for="time in dayData"
                :key="time"
                size="small"
                :type="
                  isTimeSelected(currentEditingAccount, day.date, time)
                    ? 'primary'
                    : 'default'
                "
                @click="
                  toggleTimeSelection(currentEditingAccount, day.date, time)
                "
                class="time-button"
              >
                {{ time }}
              </AButton>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <AButton @click="closeTimeSelectionModal">取消</AButton>
          <AButton type="primary" @click="confirmTimeSelection">确认</AButton>
        </div>
      </div>
    </AModal>

    <!-- 成品视频列表弹窗 -->
    <ProductVideoListModal
      v-model:visible="productVideoModalVisible"
      :product-id="selectedProductId"
      :product-title="selectedProductTitle"
      :type="2"
    />

    <!-- 添加账户弹窗 -->
    <AddAccountModal
      v-model:visible="addAccountModalVisible"
      :platform-options="platformOptions"
      @success="handleAddAccountSuccess"
    />
  </Page>
</template>

<style scoped lang="scss">
@media (max-width: 768px) {
  .matrix-dashboard {
    padding: 12px;
  }

  .stats-value {
    font-size: 24px;
  }

  .stats-label {
    font-size: 12px;
  }

  .module-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .module-icon {
    width: 40px;
    height: 40px;
    font-size: 24px;
  }

  .module-title {
    font-size: 16px;
  }

  .module-description {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stats-main-value {
    font-size: 28px;
  }

  .stats-value {
    font-size: 16px;
  }

  .modules-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .stats-card-container {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .stats-main-value {
    font-size: 24px;
  }

  .stats-value {
    font-size: 14px;
  }
}

// 暗黑模式下的平台选择样式
@media (prefers-color-scheme: dark) {
  .platform-option {
    color: var(--ant-color-text);
    background: var(--ant-color-bg-container);
    border-color: var(--ant-color-border);

    &:hover {
      background: var(--ant-color-bg-container-hover);
      border-color: var(--ant-color-primary);
    }
  }

  .platform-name {
    color: var(--ant-color-text);
  }

  .step-actions {
    border-top-color: var(--ant-color-border);
  }
}

// 暗黑模式下的抽屉样式
@media (prefers-color-scheme: dark) {
  .material-item,
  .title-item {
    color: var(--ant-color-text);
    background: var(--ant-color-bg-container);
    border-color: var(--ant-color-border);

    &:hover {
      background: var(--ant-color-bg-container-hover);
      border-color: var(--ant-color-primary);
    }

    &.selected {
      background: var(--ant-color-primary-bg);
      border-color: var(--ant-color-primary);
    }
  }

  .material-title,
  .title-text {
    color: var(--ant-color-text);
  }

  .material-time,
  .title-time {
    color: var(--ant-color-text-secondary);
  }

  .drawer-pagination,
  .drawer-actions {
    border-top-color: var(--ant-color-border);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .material-item,
  .title-item {
    padding: 12px;
    margin-bottom: 8px;
  }

  .material-title {
    font-size: 14px;
  }

  .title-text {
    font-size: 13px;
  }

  .drawer-actions {
    padding: 12px 0;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .task-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .task-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 576px) {
  .task-cards-container {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 20px;
  }
}

.matrix-dashboard {
  min-height: 100vh;
  padding: 20px;
}

// 主要内容区域样式
.main-content-section {
  margin-bottom: 32px;
}

// 左侧统计卡片样式
.stats-card-container {
  position: relative;
  padding: 24px;
  overflow: hidden;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgb(102 126 234 / 30%);
}

.stats-card-container::before {
  position: absolute;
  inset: 0;
  pointer-events: none;
  content: '';
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.stats-card-header {
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}

.stats-title {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 500;
  opacity: 0.9;
}

.stats-main-value {
  font-size: 36px;
  font-weight: bold;
  line-height: 1;
}

.stats-grid {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stats-item {
  text-align: center;
}

.stats-platform {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.2;
  opacity: 0.8;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

// 右侧功能模块网格样式
.modules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.douyin {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  }

  &.kuaishou {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
  }

  &.shipinghao {
    background: linear-gradient(135deg, #66bb6a 0%, #43a047 100%);
  }

  &.xiaohong {
    background: linear-gradient(135deg, #ec407a 0%, #e91e63 100%);
  }

  &.exposure {
    background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  }

  &.likes {
    background: linear-gradient(135deg, #ab47bc 0%, #8e24aa 100%);
  }

  &.comments {
    background: linear-gradient(135deg, #26c6da 0%, #00acc1 100%);
  }

  :deep(.ant-card-body) {
    padding: 20px;
  }
}

.stats-content {
  color: white;
  text-align: center;
}

.stats-label {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

// 功能模块区域样式
.modules-section {
  margin-bottom: 24px;
}

.module-card {
  cursor: pointer;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
    transform: translateY(-4px);
  }

  :deep(.ant-card-body) {
    padding: 24px;
  }
}

.module-content {
  position: relative;
  display: flex;
  gap: 16px;
  align-items: center;
}

.module-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  font-size: 32px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
}

.module-info {
  flex: 1;
}

.module-title {
  margin: 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.module-description {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.video-publish-button {
  height: 28px;
  padding: 0 12px;
  font-size: 12px;
  border-radius: 14px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);

  &:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    transform: translateY(-1px);
  }
}

// 列表区域样式
.list-section {
  .list-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);

    :deep(.ant-card-body) {
      padding: 24px;
    }
  }
}

// 平台选择对话框样式
.platform-select-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 20px 0;
}

.platform-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  cursor: pointer;
  background: #fafafa;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    background: #f6f9ff;
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgb(24 144 255 / 15%);
    transform: translateY(-2px);
  }
}

.platform-icon {
  margin-bottom: 12px;
  font-size: 32px;
}

.platform-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

// 发布表单样式
.publish-steps {
  margin-bottom: 32px;
}

.step-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-form {
  max-height: 500px;
  padding-right: 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.step-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 24px;
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
}

// 抽屉内容样式
.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

// 素材列表样式
.material-list {
  flex: 1;
  margin-bottom: 20px;
  overflow-y: auto;
}

.material-radio-group {
  width: 100%;

  :deep(.ant-radio-wrapper) {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }
}

.material-item {
  width: 100%;

  &.material-disabled {
    .material-radio {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      opacity: 0.6;

      &:hover {
        border-color: #d9d9d9;
      }
    }
  }
}

.material-radio {
  width: 100%;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d9d9d9;
  }

  :deep(.ant-radio) {
    align-self: flex-start;
    margin-top: 2px;
  }
}

.material-content {
  flex: 1;
  margin-left: 8px;
}

.material-title {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #333;

  .disabled-tag {
    font-size: 12px;
    font-weight: normal;
    color: #ff4d4f;
  }
}

.material-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.material-actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.material-time {
  font-size: 12px;
  color: #999;
}

.material-count {
  font-size: 12px;
  font-weight: 500;
  color: #1890ff;

  &.count-zero {
    color: #ff4d4f;
  }
}

// 标题列表样式
.title-list {
  flex: 1;
  margin-bottom: 20px;
  overflow-y: auto;
}

.title-checkbox-group {
  width: 100%;

  :deep(.ant-checkbox-wrapper) {
    width: 100%;
    margin-right: 0;
    margin-bottom: 12px;
  }
}

.title-item {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;

  &:hover {
    .title-edit-btn {
      opacity: 1;
    }
  }
}

.title-checkbox {
  flex: 1;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d9d9d9;
  }

  :deep(.ant-checkbox) {
    align-self: flex-start;
    margin-top: 2px;
  }
}

.title-edit-btn {
  height: auto;
  padding: 2px 6px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

.title-content {
  flex: 1;
  width: 500px;
  margin-left: 8px;
}

.title-text {
  display: -webkit-box;
  max-height: 60px;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  word-wrap: break-word;
  -webkit-box-orient: vertical;
}

.title-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
}

.title-time {
  font-size: 12px;
  color: #999;
}

// 抽屉分页和操作按钮
.drawer-pagination {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

.drawer-actions {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

// 暗黑主题适配
:deep(.ant-modal-content) {
  background: var(--ant-color-bg-container);
}

:deep(.ant-modal-header) {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
}

:deep(.ant-modal-title) {
  color: var(--ant-color-text);
}

:deep(.ant-form-item-label > label) {
  color: var(--ant-color-text);
}

:deep(.ant-steps-item-title) {
  color: var(--ant-color-text) !important;
}

:deep(.ant-drawer-content) {
  background: var(--ant-color-bg-container);
}

:deep(.ant-drawer-header) {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
}

:deep(.ant-drawer-title) {
  color: var(--ant-color-text);
}

// 失效账户样式
.expired-account {
  color: #ff4d4f !important;
  opacity: 0.6;
}

.expired-tag {
  margin-left: 4px;
  font-size: 12px;
  color: #ff4d4f;
}

:deep(.ant-select-item-option-disabled) {
  .expired-account {
    color: #ff4d4f !important;
  }

  .expired-tag {
    color: #ff4d4f !important;
  }
}

// 时间选择样式
.time-selection-container {
  margin-top: 16px;

  h4 {
    margin-bottom: 16px;
    font-weight: 600;
  }
}

// 账户卡片样式
.account-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.account-card {
  padding: 16px;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgb(24 144 255 / 10%);
  }
}

.account-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.account-avatar {
  flex-shrink: 0;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-name {
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
}

.account-status {
  font-size: 12px;

  .time-selected {
    font-weight: 500;
    color: #52c41a;
  }

  .time-unselected {
    color: #8c8c8c;
  }
}

.account-time-section {
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  h5 {
    margin-bottom: 16px;
    font-weight: 500;
    color: #1890ff;
  }
}

.day-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.day-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;

  .day-label {
    font-weight: 500;
    color: #262626;
  }
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;

  .time-button {
    height: 32px;
    font-size: 12px;
  }
}

// 时间选择弹窗样式
.time-selection-modal {
  .modal-account-info {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .modal-account-name {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }

  .time-selection-content {
    max-height: 400px;
    overflow-y: auto;

    .day-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

// 文案选择样式
.copywriting-radio-group {
  width: 100%;

  .copywriting-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0;
    margin-bottom: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgb(24 144 255 / 10%);

      .copywriting-edit-btn {
        opacity: 1;
      }
    }

    .copywriting-radio {
      flex: 1;
      padding: 16px;
      margin: 0;

      .copywriting-content {
        width: 100%;
        margin-left: 8px;

        .copywriting-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .copywriting-title {
            flex: 1;
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }

          .copywriting-header-right {
            display: flex;
            gap: 8px;
            align-items: center;
          }

          .copywriting-time {
            font-size: 12px;
            color: #8c8c8c;
          }
        }

        .copywriting-answer {
          display: -webkit-box;
          max-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 3;
          font-size: 13px;
          line-height: 1.5;
          color: #595959;
          -webkit-box-orient: vertical;
        }
      }
    }

    .copywriting-edit-btn {
      flex-shrink: 0;
      height: auto;
      padding: 2px 6px;
      font-size: 12px;
      opacity: 0;
      transition: opacity 0.2s;
    }
  }
}

// 编辑弹窗样式
.title-edit-modal,
.copywriting-edit-modal {
  .ant-modal-header {
    text-align: center;
    border-bottom: 1px solid hsl(var(--border));
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .ant-modal-body {
    padding: 24px;
  }
}

.edit-form-content {
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }
}

.edit-modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// 任务卡片区域样式
.tasks-section {
  margin-top: 32px;
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 24px;
  text-align: center;
}

.section-title {
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.section-description {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.task-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.task-pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.task-tabs {
  margin-bottom: 24px;

  :deep(.ant-tabs-tab) {
    font-weight: 500;
  }

  :deep(.ant-tabs-tab-active) {
    font-weight: 600;
  }
}

// 视频号标题输入组件样式
.title-input-container {
  .title-input-item {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 8px;

    .ant-input {
      flex: 1;
    }
  }
}
</style>
