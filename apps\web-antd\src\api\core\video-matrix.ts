import { requestClient } from '#/api/request';

/**
 * 视频素材列表请求参数接口
 */
export interface VideoMaterialListParams {
  is_publishe?: string; // "1"
  page: number;
  psize: number;
  status?: number; // 1
}

/**
 * 视频素材数据项接口
 */
export interface VideoMaterialItem {
  background_img_id: number;
  create_time: string;
  delete_time: null | string;
  error_msg: null | string;
  id: number;
  log_no: string;
  model_id: string;
  multiple: number;
  status: number;
  title: string;
  title_show_time: number;
  uid: number;
  update_time: string;
  video_count: number;
}

/**
 * 视频素材列表响应接口
 */
export interface VideoMaterialListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: VideoMaterialItem[];
}

/**
 * 标题列表请求参数接口
 */
export interface TitleListParams {
  page: number;
  psize: number;
}

/**
 * 标题数据项接口
 */
export interface TitleItem {
  answer: string;
  challenges: string;
  create_time: string;
  delete_time: null | string;
  desc: string;
  id: number;
  name: null | string;
  question: string;
  rows: any | null;
  title: string;
  type: any | null;
  uid: number;
  update_time: null | string;
}

/**
 * 标题列表响应接口
 */
export interface TitleListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: TitleItem[];
}

/**
 * 获取视频素材列表
 * @param params 请求参数
 * @returns Promise<VideoMaterialListResponse>
 */
export function getVideoMaterialList(
  params: VideoMaterialListParams,
): Promise<VideoMaterialListResponse> {
  return requestClient.post('/mobile/Clip/clipTaskList', params);
}

/**
 * 获取标题列表
 * @param params 请求参数
 * @returns Promise<TitleListResponse>
 */
export function getTitleList(
  params: TitleListParams,
): Promise<TitleListResponse> {
  return requestClient.post('/mobile/aiCreate/titleList', params);
}

/**
 * 分组获取请求参数接口
 */
export interface AccountGroupParams {
  type: number; // 1抖音2快手3视频号4小红书
}

/**
 * 账号分组数据项接口
 */
export interface AccountGroupItem {
  account_count: number;
  comment: number;
  create_time: string;
  delete_time: null | string;
  exposure_count: number;
  id: number;
  like_count: number;
  name: string;
  type: number;
  uid: number;
  update_time: string;
}

/**
 * 获取分组下账号请求参数接口
 */
export interface AccountListParams {
  account_group_id: number; // 已选择的分组的id
  page: number; // 1
  psize: number; // 200
  type: number; // 1抖音2快手3视频号4小红书
}

/**
 * 分组账号列表请求参数接口
 */
export interface GroupAccountListParams {
  account_group_id: number; // 分组ID
  limit: number; // 每页数量，默认10
  page: number; // 页码
  type: number; // 平台类型：1抖音2快手3视频号4小红书
}

/**
 * 分组账号数据项接口
 */
export interface GroupAccountItem {
  access_token: null | string;
  account_group_id: number;
  account_group_name: string;
  account_name: string;
  avatar: string;
  city_id: number;
  comment: number;
  cookie: string;
  create_time: string;
  customize_account_avatar: null | string;
  customize_account_name: null | string;
  delete_time: null | string;
  douyin_uid: string;
  expires_in: string;
  exposure_count: number;
  id: number;
  is_delete: number;
  like_count: number;
  open_id: null | string;
  percentage: string;
  province_id: number;
  published_task: number;
  qrcode_id: number;
  red_id: null | string;
  refresh_token: null | string;
  refresh_token_expires_in: null | string;
  released_task: number;
  status: number;
  totle_task: number;
  type: number;
  uid: number;
  update_time: string;
}

/**
 * 分组账号列表响应接口
 */
export interface GroupAccountListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: GroupAccountItem[];
}

/**
 * 账号数据项接口
 */
export interface AccountItem {
  access_token: null | string;
  account_group_id: number;
  account_group_name: string;
  account_name: string;
  avatar: string;
  city_id: number;
  comment: number;
  cookie: string;
  create_time: string;
  customize_account_avatar: null | string;
  customize_account_name: null | string;
  delete_time: null | string;
  douyin_uid: string;
  expires_in: string;
  exposure_count: number;
  id: number;
  is_delete: number;
  like_count: number;
  open_id: null | string;
  percentage: string;
  province_id: number;
  published_task: number;
  qrcode_id: number;
  red_id: null | string;
  refresh_token: null | string;
  refresh_token_expires_in: null | string;
  released_task: number;
  status: number;
  totle_task: number;
  type: number;
  uid: number;
  update_time: string;
}

/**
 * 账号列表响应接口
 */
export interface AccountListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: AccountItem[];
}

/**
 * 获取账号分组列表
 * @param params 请求参数
 * @returns Promise<AccountGroupItem[]>
 */
export function getAccountGroupList(
  params: AccountGroupParams,
): Promise<AccountGroupItem[]> {
  return requestClient.post('/mobile/externalAccount/accountGroupUser', params);
}

/**
 * 分组管理列表请求参数接口
 */
export interface GroupManagementListParams {
  page: number; // 页码
  psize: number; // 每页数量，默认12
  type: number; // 平台类型：1抖音2快手3视频号4小红书
}

/**
 * 分组管理列表响应接口
 */
export interface GroupManagementListResponse {
  pindex: number; // 当前页码
  psize: number; // 每页数量
  total: number; // 总数量
  totalPage: number; // 总页数
  list: AccountGroupItem[]; // 分组列表
}

/**
 * 获取分组管理列表
 * @param params 请求参数
 * @returns Promise<GroupManagementListResponse>
 */
export function getGroupManagementList(
  params: GroupManagementListParams,
): Promise<GroupManagementListResponse> {
  return requestClient.post('/mobile/externalAccount/accountGroupList', params);
}

/**
 * 添加分组请求参数接口
 */
export interface AddAccountGroupParams {
  name: string; // 分组名称
  type: number; // 平台类型：1抖音2快手3视频号4小红书
}

/**
 * 删除分组请求参数接口
 */
export interface DelAccountGroupParams {
  id: number; // 分组ID
}

/**
 * 添加账号分组
 * @param params 请求参数
 * @returns Promise<any>
 */
export function addAccountGroup(params: AddAccountGroupParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/addAccountGroup', params);
}

/**
 * 删除账号分组
 * @param params 请求参数
 * @returns Promise<any>
 */
export function delAccountGroup(params: DelAccountGroupParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/delAccountGroup', params);
}

/**
 * 获取分组账号列表
 * @param params 请求参数
 * @returns Promise<GroupAccountListResponse>
 */
export function getGroupAccountList(
  params: GroupAccountListParams,
): Promise<GroupAccountListResponse> {
  return requestClient.post('/mobile/externalAccount/accountList', params);
}

/**
 * 删除账户请求参数接口
 */
export interface DelAccountParams {
  id: number[]; // 账户ID数组
}

/**
 * 账户换组请求参数接口
 */
export interface AccountExchangeGroupParams {
  account_group_id: number; // 切换的分组ID
  id: number[]; // 账户ID数组
}

/**
 * 删除账户
 * @param params 请求参数
 * @returns Promise<any>
 */
export function delAccount(params: DelAccountParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/delAccount', params);
}

/**
 * 账户换组
 * @param params 请求参数
 * @returns Promise<any>
 */
export function accountExchangeGroup(
  params: AccountExchangeGroupParams,
): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/accountExchangeGroup',
    params,
  );
}

/**
 * 获取分组下的账号列表
 * @param params 请求参数
 * @returns Promise<AccountListResponse>
 */
export function getAccountList(
  params: AccountListParams,
): Promise<AccountListResponse> {
  return requestClient.post('/mobile/externalAccount/accountList', params);
}

/**
 * POI搜索请求参数接口
 */
export interface PoiSearchParams {
  keyword: string; // 商家简称
  city: string; // 城市code
}

/**
 * POI数据项接口
 */
export interface PoiItem {
  address: string;
  city: string;
  city_code: string;
  country: string;
  country_code: string;
  district: string;
  location: string;
  poi_id: string;
  poi_name: string;
  province: string;
}

/**
 * 搜索POI地址
 * @param params 请求参数
 * @returns Promise<PoiItem[]>
 */
export function searchPoiList(params: PoiSearchParams): Promise<PoiItem[]> {
  return requestClient.get('/mobile/externalAccount/poiList', { params });
}

/**
 * 文案列表请求参数接口
 */
export interface CopywritingListParams {
  page: number;
  psize: number;
}

/**
 * 文案数据项接口
 */
export interface CopywritingItem {
  id: number;
  name: string;
  answer: string;
  create_time: string;
  desc: null | string;
  guide: string;
  is_status: number;
  jobId: null | string;
  price: string;
  product: string;
  question: null | string;
  rows: number;
  sid: string;
  store_name: string;
  type: number;
  uid: number;
  update_time: string;
  words: number;
  delete_time: null | string;
}

/**
 * 文案列表响应接口
 */
export interface CopywritingListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: CopywritingItem[]; // 根据实际API返回，这里是data字段包含数组
}

/**
 * 获取文案列表
 * @param params 请求参数
 * @returns Promise<CopywritingListResponse>
 */
export function getCopywritingList(
  params: CopywritingListParams,
): Promise<CopywritingListResponse> {
  return requestClient.post('/mobile/aiCreate/createList', params);
}

/**
 * 发布任务请求参数接口
 */
export interface PublishTaskParams {
  name: string;
  clip_id: number;
  title_id: string;
  release_type: number;
  set_time: any[];
  startDate: string;
  release_everyday_count: number;
  release_day_count: number;
  type: number;
  mount_type?: number;
  poi?: string;
  poi_address?: string;
  merchant_product_id?: string;
  copywiring?: string;
  goods_id?: string;
}

/**
 * 添加视频发布任务
 * @param params 请求参数
 * @returns Promise<any> 响应拦截器直接返回data字段
 */
export function addVideoTask(params: PublishTaskParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/addVideoTask', params);
}

/**
 * 任务列表请求参数接口
 */
export interface VideoTaskListParams {
  page: number;
  psize: number;
  type: string; // ""为空是全部，1抖音2快手3视频号4小红书
}

/**
 * 任务列表数据项接口
 */
export interface VideoTaskItem {
  anchorId: null | string;
  anchorTitle: null | string;
  anchorUrl: null | string;
  channle_type: number; // 1抖音2快手3视频号4小红书
  city: string;
  create_time: string;
  delete_time: null | string;
  goods_id: null | string;
  id: number;
  mount_type: number;
  name: string;
  percentage: string; // 进度百分比，例如"50"
  poi: string;
  poi_address: string;
  published_fail_task: number; // 发布失败数量
  published_task: number; // 发布成功数量
  published_wait_task: number; // 待发布数量
  release_day_count: number;
  release_everyday_count: number;
  release_time: string;
  release_type: number;
  shop_name: string;
  startDate: number;
  status: number; // 1显示终止任务 2已终止 3已完成（2和3的时候显示删除任务按钮）
  three_account_id: string;
  topic_list: any;
  topic_name: null | string;
  totle_task: number;
  uid: number;
  update_time: string;
}

/**
 * 任务列表响应接口
 */
export interface VideoTaskListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: VideoTaskItem[];
}

/**
 * 获取任务列表
 * @param params 请求参数
 * @returns Promise<VideoTaskListResponse>
 */
export function getVideoTaskList(
  params: VideoTaskListParams,
): Promise<VideoTaskListResponse> {
  return requestClient.post('/mobile/externalAccount/VideoTaskList', params);
}

/**
 * 任务统计数据请求参数接口
 */
export interface VideoTaskStatisticsParams {
  id?: string; // 任务详情时传任务ID
  three_account_id?: string; // 账户详情时传账户ID
}

/**
 * 任务统计数据响应接口
 */
export interface VideoTaskStatisticsResponse {
  comment: number; // 评论数
  exposure_count: number; // 曝光量
  like_count: number; // 点赞量
  published_fail_task: number; // 发布失败数量
  published_task: number; // 发布成功数量
  published_wait_task: number; // 待发布数量
  totle_task: number; // 总任务数
}

/**
 * 获取任务统计数据
 * @param params 请求参数
 * @returns Promise<VideoTaskStatisticsResponse>
 */
export function getVideoTaskStatistics(
  params: VideoTaskStatisticsParams,
): Promise<VideoTaskStatisticsResponse> {
  return requestClient.post(
    '/mobile/ExternalAccount/VideoTaskStatistics',
    params,
  );
}

/**
 * 任务详情列表请求参数接口
 */
export interface VideoTaskDetailListParams {
  task_id: string; // type=2时传任务ID
  three_account_id: string; // type=1时传账户ID
  status: string; // 菜单id
  type: number; // 渠道id
  page: number;
  limit: number;
}

/**
 * 任务详情列表数据项接口
 */
export interface VideoTaskDetailItem {
  account_name: string;
  ai_title: number;
  anchorId: null | string;
  anchorTitle: null | string;
  anchorUrl: null | string;
  avatar: string;
  channle_type: number;
  clip_project_video_id: number;
  comment: null | number;
  copywriting_id: null | string;
  create_time: string;
  delete_time: null | string;
  exposure_count: null | number;
  goods_id: null | string;
  id: number;
  is_hang: number;
  item_id: null | string;
  like_count: null | number;
  log_no: string;
  logid: null | string;
  merchant_product_id: null | string;
  name: string;
  photo_id: null | string;
  release_time: string;
  share_link: null | string;
  status: number;
  task_id: null | string;
  three_account_id: string;
  three_video_task_id: number;
  topic_list: any;
  topic_name: null | string;
  uid: number;
  update_time: null | string;
  video_cover: string;
  video_src: string;
  xiaohongshu_id: null | string;
  hint?: string; // 失败原因（可选字段）
}

/**
 * 任务详情列表响应接口
 */
export interface VideoTaskDetailListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: VideoTaskDetailItem[];
}

/**
 * 获取任务详情列表
 * @param params 请求参数
 * @returns Promise<VideoTaskDetailListResponse>
 */
export function getVideoTaskDetailList(
  params: VideoTaskDetailListParams,
): Promise<VideoTaskDetailListResponse> {
  return requestClient.post('/mobile/ExternalAccount/VideoList', params);
}

/**
 * 抖音评论列表请求参数接口
 */
export interface DouyinCommentListParams {
  id: number; // 列表项的id
  cursor: number; // 页数
  count: number; // 每页数量
}

/**
 * 抖音评论用户信息接口
 */
export interface DouyinCommentUser {
  avatar_thumb: {
    url_list: string[];
  };
  nickname: string;
}

/**
 * 抖音评论项接口
 */
export interface DouyinCommentItem {
  user: DouyinCommentUser;
  create_time: number;
  text: string;
}

/**
 * 抖音评论列表响应接口
 */
export interface DouyinCommentListResponse {
  comment_common_data: string;
  comment_config: any[];
  comments: DouyinCommentItem[] | null;
  cursor: number;
  extra: {
    fatal_item_ids: any;
    now: number;
  };
  fatal_item_ids: any;
  now: number;
  fast_response_comment: {
    constant_response_words: string[];
    timed_response_words: string[];
  };
  folded_comment_count: number;
  general_comment_config: any[];
  has_more: number;
  hotsoon_filtered_count: number;
  log_pb: {
    impr_id: string;
  };
  reply_style: number;
  show_management_entry_point: number;
  status_code: number;
  total: number;
  user_commented: number;
}

/**
 * 获取抖音评论列表
 * @param params 请求参数
 * @returns Promise<DouyinCommentListResponse>
 */
export function getDouyinCommentList(
  params: DouyinCommentListParams,
): Promise<DouyinCommentListResponse> {
  return requestClient.post(
    '/mobile/externalAccount/douyinCommentList',
    params,
  );
}

/**
 * 矩阵管理统计数据响应接口
 */
export interface MatrixStatisticsResponse {
  account_count: number; // 累计发布账户
  comment_total: number; // 累计评价
  douyin_account_count: number; // 抖音账户
  exposure_count_total: number; // 累计曝光
  kuaishou_account_count: number; // 快手账户
  like_count_total: number; // 累计点赞
  shipinghao_account_count: number; // 视频号账户
  xiaohong_account_count: number; // 小红薯账户
}

/**
 * 获取矩阵管理统计数据
 * @returns Promise<MatrixStatisticsResponse>
 */
export function getMatrixStatistics(): Promise<MatrixStatisticsResponse> {
  return requestClient.post('/mobile/externalAccount/statistics');
}

/**
 * 发布操作请求参数接口
 */
export interface PublishParams {
  id: number; // 列表项的id
}

/**
 * 发布抖音视频
 * @param params 请求参数
 * @returns Promise<any>
 */
export function publishDouyin(params: PublishParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/publishDouyin', params);
}

/**
 * 发布快手视频
 * @param params 请求参数
 * @returns Promise<any>
 */
export function publishKuaishou(params: PublishParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/publisheKuaishou', params);
}

/**
 * 发布小红书视频
 * @param params 请求参数
 * @returns Promise<any>
 */
export function publishXiaohongshu(params: PublishParams): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/releaseXiaohongshu',
    params,
  );
}

/**
 * 任务操作请求参数接口
 */
export interface TaskOperationParams {
  id: number; // 任务ID
}

/**
 * 终止任务
 * @param params 请求参数
 * @returns Promise<any>
 */
export function endTask(params: TaskOperationParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/endTask', params);
}

/**
 * 删除任务
 * @param params 请求参数
 * @returns Promise<any>
 */
export function deleteTask(params: TaskOperationParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/delTask', params);
}

/**
 * 成品视频列表请求参数接口
 */
export interface ProductVideoListParams {
  id: number; // 成品id
  page: number;
  psize: number;
}

/**
 * 成品视频数据项接口
 */
export interface ProductVideoItem {
  audio_jobid: string;
  audio_media_id: string;
  audio_url: string;
  background_img_media_id: string;
  background_img_url: string;
  background_music_media_id: string;
  background_music_url: string;
  complete_cover: string;
  complete_jobid: string;
  complete_video: string;
  content: string;
  create_time: string;
  delete_time: null | string;
  dy_qrcode: string;
  error_msg: null | string;
  id: number;
  is_douyin: number;
  is_kuaishou: number;
  is_shipinghao: number;
  is_xiaohongshu: number;
  ks_qrcode: null | string;
  mission_id: number;
  status: number;
  title: string;
  uid: number;
  video_media_id: string;
  video_url: string;
}

/**
 * 成品视频列表响应接口
 */
export interface ProductVideoListResponse {
  pindex: number;
  psize: number;
  total: number;
  totalPage: number;
  list: ProductVideoItem[];
}

/**
 * 转发设置响应接口
 */
export interface RepostSettingResponse {
  create_time: string;
  delete_time: null | string;
  id: number;
  is_open: number;
  update_time: string;
}

/**
 * 获取成品视频列表
 * @param params 请求参数
 * @returns Promise<ProductVideoListResponse>
 */
export function getProductVideoList(
  params: ProductVideoListParams,
): Promise<ProductVideoListResponse> {
  return requestClient.post('/mobile/Clip/clipVideoList', params);
}

/**
 * 获取转发设置
 * @returns Promise<RepostSettingResponse>
 */
export function getRepostSetting(): Promise<RepostSettingResponse> {
  return requestClient.post('/mobile/repost/set');
}

/**
 * 转发视频请求参数接口
 */
export interface RepostVideoParams {
  video_id: number; // 视频的id字段
  way: number; // 1表示抖音，2表示快手
  type: number; // 1表示从成品列表页面进入，2表示从素材列表进入
}

/**
 * 转发视频
 * @param params 请求参数
 * @returns Promise<RepostVideoResponse>
 */
export function repostVideo(params: RepostVideoParams): Promise<string> {
  return requestClient.post('/mobile/repost/clipVideoExhibit', params);
}

/**
 * 城市省份数据项接口
 */
export interface CityProvinceItem {
  value: number;
  label: string;
  children: Array<{
    label: string;
    value: number;
  }>;
}

/**
 * 获取城市省份列表
 * @returns Promise<CityProvinceItem[]>
 */
export function getCityProvinceList(): Promise<CityProvinceItem[]> {
  return requestClient.post('/mobile/index/liuGuanProvince');
}

/**
 * 抖音二维码请求参数接口
 */
export interface DouyinQrcodeParams {
  account_group_id: number;
  city_id: number;
  province_id: number;
}

/**
 * 抖音二维码响应接口
 */
export interface DouyinQrcodeResponse {
  account_group_id: string;
  city_id: string;
  create_time: string;
  expire_time: number;
  id: string;
  province_id: string;
  proxy: string;
  qrcode: string;
  qrcode_index_url: string;
  token: string;
  uid: string;
  update_time: string;
}

/**
 * 快手二维码请求参数接口
 */
export interface KuaishouQrcodeParams {
  account_group_id: number;
}

/**
 * 快手二维码响应接口
 */
export interface KuaishouQrcodeResponse {
  account_group_id: string;
  create_time: string;
  expire_time: number;
  id: string;
  qrcode: string;
  uid: string;
  update_time: string;
}

/**
 * 视频号二维码请求参数接口
 */
export interface ShipinghaoQrcodeParams {
  account_group_id: number;
  customize_account_name: string;
}

/**
 * 视频号二维码响应接口
 */
export interface ShipinghaoQrcodeResponse {
  account_group_id: string;
  city_id: string;
  create_time: string;
  customize_account_avatar: string;
  customize_account_name: string;
  id: string;
  province_id: string;
  qrcode: string;
  token: string;
  uid: string;
  update_time: string;
}

/**
 * 小红书二维码请求参数接口
 */
export interface XiaohongshuQrcodeParams {
  account_group_id: number;
}

/**
 * 小红书二维码响应接口
 */
export interface XiaohongshuQrcodeResponse {
  account_group_id: string;
  code: string;
  cookie: string;
  create_time: string;
  expire_time: number;
  id: string;
  qr_id: string;
  qrcode: string;
  uid: string;
  update_time: string;
}

/**
 * 二维码检查请求参数接口
 */
export interface QrcodeCheckParams {
  id: string;
}

/**
 * 抖音验证短信请求参数接口
 */
export interface DouyinVerifySmsParams {
  id: string;
  code: string;
}

/**
 * 小红书Cookie绑定请求参数接口
 */
export interface XiaohongshuCookieParams {
  account_group_id: number; // 分组ID
  cookie: string; // Cookie字符串
}

/**
 * 获取抖音二维码
 * @param params 请求参数
 * @returns Promise<DouyinQrcodeResponse>
 */
export function getDouyinQrcode(
  params: DouyinQrcodeParams,
): Promise<DouyinQrcodeResponse> {
  return requestClient.post('/mobile/externalAccount/douyinQrcode', params);
}

/**
 * 获取快手二维码
 * @param params 请求参数
 * @returns Promise<KuaishouQrcodeResponse>
 */
export function getKuaishouQrcode(
  params: KuaishouQrcodeParams,
): Promise<KuaishouQrcodeResponse> {
  return requestClient.post('/mobile/externalAccount/kuaishouQrcode', params);
}

/**
 * 获取视频号二维码
 * @param params 请求参数
 * @returns Promise<ShipinghaoQrcodeResponse>
 */
export function getShipinghaoQrcode(
  params: ShipinghaoQrcodeParams,
): Promise<ShipinghaoQrcodeResponse> {
  return requestClient.post('/mobile/externalAccount/shipinghaoQrcode', params);
}

/**
 * 获取小红书二维码
 * @param params 请求参数
 * @returns Promise<XiaohongshuQrcodeResponse>
 */
export function getXiaohongshuQrcode(
  params: XiaohongshuQrcodeParams,
): Promise<XiaohongshuQrcodeResponse> {
  return requestClient.post(
    '/mobile/externalAccount/xiaohongshuQrcode',
    params,
  );
}

/**
 * 检查抖音二维码状态
 * @param params 请求参数
 * @returns Promise<any>
 */
export function checkDouyinQrcode(params: QrcodeCheckParams): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/douyinQrcodeCheck',
    params,
    {
      silentError: true, // 静默处理错误，不显示弹窗
    },
  );
}

/**
 * 抖音验证短信
 * @param params 请求参数
 * @returns Promise<any>
 */
export function douyinVerifySms(params: DouyinVerifySmsParams): Promise<any> {
  return requestClient.post('/mobile/externalAccount/douyinVerifySms', params);
}

/**
 * 检查视频号二维码状态
 * @param params 请求参数
 * @returns Promise<any>
 */
export function checkShipinghaoQrcode(params: QrcodeCheckParams): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/shipinghaoeQrcodeCheck',
    params,
    {
      silentError: true, // 静默处理错误，不显示弹窗
    },
  );
}

/**
 * 检查小红书用户信息
 * @param params 请求参数
 * @returns Promise<any>
 */
export function checkXiaohongshuUserInfo(
  params: QrcodeCheckParams,
): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/xiaohongshuUserInfo',
    params,
    {
      silentError: true, // 静默处理错误，不显示弹窗
    },
  );
}

/**
 * 小红书Cookie绑定
 * @param params 请求参数
 * @returns Promise<any>
 */
export function bindXiaohongshuCookie(
  params: XiaohongshuCookieParams,
): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/xiaohongshuCookie',
    params,
  );
}

/**
 * 删除成品库参数接口
 */
export interface DeleteMaterialParams {
  id: number; // 列表项id
}

/**
 * 删除成品库
 * @param params 删除参数
 * @returns Promise<any>
 */
export function deleteMaterial(params: DeleteMaterialParams): Promise<any> {
  return requestClient.post('/mobile/clip/deleteMission', params);
}

/**
 * 更新标题参数接口
 */
export interface UpdateTitleParams {
  answer: string; // 标题
  challenges: string; // 话题
  id: number; // 标题ID
}

/**
 * 更新文案参数接口
 */
export interface UpdateCopywritingParams {
  content: string; // 文案内容
  id: number; // 文案ID
}

/**
 * 更新标题
 * @param params 更新参数
 * @returns Promise<any>
 */
export function updateTitle(params: UpdateTitleParams): Promise<any> {
  return requestClient.post('/mobile/ai_title/update', params);
}

/**
 * 更新文案
 * @param params 更新参数
 * @returns Promise<any>
 */
export function updateCopywriting(
  params: UpdateCopywritingParams,
): Promise<any> {
  return requestClient.post('/mobile/ai_create_v2/update', params);
}
