<script setup lang="ts">
import type {
  AccountGroupItem,
  AddAccountGroupParams,
  CityProvinceItem,
  DouyinQrcodeParams,
  GroupManagementListParams,
  KuaishouQrcodeParams,
  QrcodeCheckParams,
  ShipinghaoQrcodeParams,
  XiaohongshuQrcodeParams,
} from '#/api/core';

import { computed, ref, watch } from 'vue';

import {
  Button as AButton,
  Cascader as ACascader,
  Col as ACol,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Spin as ASpin,
  Step as AStep,
  Steps as ASteps,
  message,
} from 'ant-design-vue';

import {
  addAccountGroup,
  bindXiaohongshuCookie,
  checkDouyinQrcode,
  checkShipinghaoQrcode,
  checkXiaohongshuUserInfo,
  douyinVerifySms,
  getCityProvinceList,
  getDouyinQrcode,
  getGroupManagementList,
  getKuaishouQrcode,
  getShipinghaoQrcode,
  getXiaohongshuQrcode,
} from '#/api/core';

// Props 定义
interface Props {
  visible?: boolean;
  platformOptions?: Array<{
    icon: string;
    label: string;
    value: number;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  platformOptions: () => [],
});

// Emits 定义
const emit = defineEmits<{
  success: [];
  'update:visible': [value: boolean];
}>();

// 响应式数据
const currentStep = ref(0); // 0: 平台选择, 1: 表单填写, 2: 二维码扫描
const selectedPlatform = ref<null | number>(null);
const loading = ref(false);

// 表单数据
const formData = ref({
  // 通用字段
  groupId: undefined as number | undefined,

  // 抖音字段
  city: [] as string[], // 仅抖音平台使用

  // 视频号字段
  accountName: '',

  // 小红书字段
  bindingType: '', // 'qrcode' | 'cookie'
});

// 数据状态
const cityOptions = ref<CityProvinceItem[]>([]);
const groupOptions = ref<{ label: string; value: number }[]>([]);
const cityLoading = ref(false);
const groupLoading = ref(false);
const addGroupLoading = ref(false);

// 新增分组相关
const addGroupModalVisible = ref(false);
const newGroupName = ref('');

// 二维码相关状态
const qrcodeData = ref<any>(null);
const qrcodeLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);
const checkTimer = ref<NodeJS.Timeout | null>(null);
const scanSuccess = ref(false);
const qrcodeExpired = ref(false);
const verificationCode = ref('');
const cookieInput = ref('');

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const selectedPlatformInfo = computed(() => {
  return props.platformOptions.find((p) => p.value === selectedPlatform.value);
});

// 表单验证规则
const formRules = computed(() => {
  const rules: any = {
    groupId: [{ required: true, message: '请选择分组', trigger: 'change' }],
  };

  // 根据平台添加特定验证规则
  switch (selectedPlatform.value) {
    case 1: {
      // 抖音：城市选择必填
      rules.city = [
        { required: true, message: '请选择城市', trigger: 'change' },
      ];

      break;
    }
    case 3: {
      // 视频号：账户名称必填
      rules.accountName = [
        { required: true, message: '请输入账户名称', trigger: 'blur' },
        { max: 50, message: '账户名称不能超过50个字符', trigger: 'blur' },
      ];

      break;
    }
    case 4: {
      // 小红书：绑定类型必填
      rules.bindingType = [
        { required: true, message: '请选择绑定类型', trigger: 'change' },
      ];

      break;
    }
    // No default
  }

  return rules;
});

// 方法定义
const handlePlatformSelect = (platform: number) => {
  selectedPlatform.value = platform;
  currentStep.value = 1;

  // 重置表单数据
  resetFormData();

  // 加载对应平台的分组数据
  loadGroupOptions();

  // 如果需要城市数据，则加载城市数据（仅抖音需要）
  if (platform === 1) {
    loadCityOptions();
  }
};

const handleBack = () => {
  if (currentStep.value === 2) {
    currentStep.value = 1;
    resetQrcodeState();
  } else if (currentStep.value === 1) {
    currentStep.value = 0;
    selectedPlatform.value = null;
    resetFormData();
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  currentStep.value = 0;
  selectedPlatform.value = null;
  resetFormData();
};

const resetFormData = () => {
  formData.value = {
    groupId: undefined,
    city: [],
    accountName: '',
    bindingType: '',
  };
  cookieInput.value = '';
};

// 加载城市数据
const loadCityOptions = async () => {
  try {
    cityLoading.value = true;
    const response = await getCityProvinceList();
    cityOptions.value = response;
  } catch (error) {
    console.error('加载城市数据失败:', error);
    message.error('加载城市数据失败，请重试');
    cityOptions.value = [];
  } finally {
    cityLoading.value = false;
  }
};

// 加载分组数据
const loadGroupOptions = async () => {
  if (!selectedPlatform.value) return;

  try {
    groupLoading.value = true;
    const params: GroupManagementListParams = {
      page: 1,
      psize: 2000,
      type: selectedPlatform.value,
    };

    const response = await getGroupManagementList(params);
    groupOptions.value = response.list.map((group: AccountGroupItem) => ({
      value: group.id,
      label: group.name,
    }));
  } catch (error) {
    console.error('加载分组数据失败:', error);
    message.error('加载分组数据失败，请重试');
    groupOptions.value = [];
  } finally {
    groupLoading.value = false;
  }
};

// 新增分组
const handleAddGroup = () => {
  addGroupModalVisible.value = true;
  newGroupName.value = '';
};

const handleAddGroupSubmit = async () => {
  const name = newGroupName.value.trim();
  if (!name) {
    message.error('请输入分组名称');
    return;
  }
  if (name.length > 20) {
    message.error('分组名称不能超过20个字符');
    return;
  }

  try {
    addGroupLoading.value = true;
    const params: AddAccountGroupParams = {
      name,
      type: selectedPlatform.value!,
    };

    await addAccountGroup(params);
    message.success('添加分组成功');

    addGroupModalVisible.value = false;
    newGroupName.value = '';

    // 重新加载分组列表
    await loadGroupOptions();
  } catch (error) {
    console.error('添加分组失败:', error);
    message.error('添加分组失败，请重试');
  } finally {
    addGroupLoading.value = false;
  }
};

const handleAddGroupCancel = () => {
  addGroupModalVisible.value = false;
  newGroupName.value = '';
};

// 表单提交（进入二维码扫描步骤）
const handleSubmit = async () => {
  // 表单验证
  if (!selectedPlatform.value) {
    message.error('请选择平台');
    return;
  }

  if (!formData.value.groupId) {
    message.error('请选择分组');
    return;
  }

  // 根据平台进行特定验证
  if (selectedPlatform.value === 1 && formData.value.city.length === 0) {
    message.error('请选择城市');
    return;
  }

  if (selectedPlatform.value === 3) {
    if (!formData.value.accountName.trim()) {
      message.error('请输入账户名称');
      return;
    }
  }

  if (selectedPlatform.value === 4 && !formData.value.bindingType) {
    message.error('请选择绑定类型');
    return;
  }

  // 进入第三步（扫码授权/Cookie绑定）
  currentStep.value = 2;

  // 如果是小红书且选择cookie绑定，不需要生成二维码
  if (selectedPlatform.value === 4 && formData.value.bindingType === 'cookie') {
    return;
  }

  // 其他情况生成二维码
  await generateQrcode();
};

// 生成二维码
const generateQrcode = async () => {
  try {
    qrcodeLoading.value = true;
    resetQrcodeState();

    let response: any;

    switch (selectedPlatform.value) {
      case 1: {
        // 抖音
        const params: DouyinQrcodeParams = {
          account_group_id: formData.value.groupId!,
          city_id: Number(formData.value.city[1]),
          province_id: Number(formData.value.city[0]),
        };
        response = await getDouyinQrcode(params);
        break;
      }
      case 2: {
        // 快手
        const params: KuaishouQrcodeParams = {
          account_group_id: formData.value.groupId!,
        };
        response = await getKuaishouQrcode(params);
        break;
      }
      case 3: {
        // 视频号
        const params: ShipinghaoQrcodeParams = {
          account_group_id: formData.value.groupId!,
          customize_account_name: formData.value.accountName,
        };
        response = await getShipinghaoQrcode(params);
        break;
      }
      case 4: {
        // 小红书
        const params: XiaohongshuQrcodeParams = {
          account_group_id: formData.value.groupId!,
        };
        response = await getXiaohongshuQrcode(params);
        break;
      }
      default: {
        throw new Error('不支持的平台类型');
      }
    }

    qrcodeData.value = response;

    // 启动倒计时（抖音和视频号需要60秒倒计时）
    if (selectedPlatform.value === 1 || selectedPlatform.value === 3) {
      startCountdown();
    }

    // 启动状态检查（快手不需要）
    if (selectedPlatform.value !== 2) {
      startStatusCheck();
    }
  } catch (error) {
    console.error('生成二维码失败:', error);
    message.error('生成二维码失败，请重试');
  } finally {
    qrcodeLoading.value = false;
  }
};

// 重置二维码状态
const resetQrcodeState = () => {
  qrcodeData.value = null;
  scanSuccess.value = false;
  qrcodeExpired.value = false;
  verificationCode.value = '';
  clearTimers();
};

// 清除定时器
const clearTimers = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
  }
  if (checkTimer.value) {
    clearInterval(checkTimer.value);
    checkTimer.value = null;
  }
};

// 启动倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
      qrcodeExpired.value = true;
    }
  }, 1000);
};

// 启动状态检查
const startStatusCheck = () => {
  checkTimer.value = setInterval(async () => {
    if (!qrcodeData.value || scanSuccess.value || qrcodeExpired.value) {
      return;
    }

    try {
      const params: QrcodeCheckParams = {
        id: qrcodeData.value.id,
      };

      switch (selectedPlatform.value) {
        case 1: {
          // 抖音二维码检查 - 请求成功即停止检查并显示验证码输入框
          try {
            await checkDouyinQrcode(params);
            // 抖音二维码检查请求成功，立即设置扫描成功状态
            scanSuccess.value = true;
            clearTimers();
            // 不显示"扫描成功"消息，因为会在UI上显示
          } catch (douyinError: any) {
            // 检查抖音特定的错误码
            const errno =
              douyinError?.response?.data?.errno || douyinError?.errno;
            if (errno === -1) {
              // 抖音 errno -1 表示已过期
              qrcodeExpired.value = true;
              clearTimers();
            }
          }
          break;
        }
        case 3: {
          // 视频号二维码检查 - 请求成功即停止检查并显示扫码完成
          await checkShipinghaoQrcode(params);
          // 视频号二维码检查请求成功，立即设置扫描成功状态
          scanSuccess.value = true;
          clearTimers();
          // 1秒后自动关闭弹窗并显示绑定成功
          setTimeout(() => {
            message.success('视频号账号绑定成功');
            emit('success');
            modalVisible.value = false;
            resetForm();
          }, 1000);
          break;
        }
        case 4: {
          // 小红书二维码检查 - 请求成功即停止检查并显示扫码完成
          try {
            await checkXiaohongshuUserInfo(params);
            // 小红书二维码检查请求成功，立即设置扫描成功状态
            scanSuccess.value = true;
            clearTimers();
            // 1秒后自动关闭弹窗并显示绑定成功
            setTimeout(() => {
              message.success('小红书账号绑定成功');
              emit('success');
              modalVisible.value = false;
              resetForm();
            }, 1000);
          } catch (xiaohongshuError: any) {
            // 检查小红书特定的错误码
            const errno =
              xiaohongshuError?.response?.data?.errno ||
              xiaohongshuError?.errno;
            if (errno === -1) {
              // 小红书 errno -1 表示账号授权失败
              qrcodeExpired.value = true;
              clearTimers();
              message.error('账号授权失败');
            } else if (errno === -3) {
              // 小红书 errno -3 表示已过期
              qrcodeExpired.value = true;
              clearTimers();
            }
          }
          break;
        }
        default:
      }
    } catch (error) {
      console.error('检查二维码状态失败:', error);
    }
  }, 2000);
};

// 刷新二维码
const refreshQrcode = () => {
  generateQrcode();
};

// Cookie提交处理
const handleCookieSubmit = async () => {
  if (!cookieInput.value.trim()) {
    message.error('请输入Cookie');
    return;
  }

  if (!formData.value.groupId) {
    message.error('请选择分组');
    return;
  }

  try {
    loading.value = true;

    await bindXiaohongshuCookie({
      account_group_id: formData.value.groupId,
      cookie: cookieInput.value.trim(),
    });

    message.success('小红书Cookie绑定成功');
    emit('success');
    modalVisible.value = false;
    resetForm();
  } catch (error: any) {
    console.error('小红书Cookie绑定失败:', error);
    const errorMessage =
      error?.response?.data?.message ||
      error?.message ||
      'Cookie绑定失败，请检查Cookie是否正确';
    message.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 完成授权
const handleCompleteAuth = async () => {
  if (selectedPlatform.value === 1) {
    // 抖音平台：验证短信验证码
    if (!verificationCode.value.trim()) {
      message.error('请输入验证码');
      return;
    }

    if (!qrcodeData.value?.id) {
      message.error('二维码信息丢失，请重新生成');
      return;
    }

    try {
      loading.value = true;

      await douyinVerifySms({
        id: qrcodeData.value.id,
        code: verificationCode.value.trim(),
      });

      message.success('抖音账号绑定成功');
      emit('success');
      modalVisible.value = false;
      resetForm();
    } catch (error: any) {
      console.error('抖音验证失败:', error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        '验证失败，请检查验证码是否正确';
      message.error(errorMessage);
    } finally {
      loading.value = false;
    }
  } else {
    // 其他平台的授权逻辑
    message.success('授权完成');
    emit('success');
    modalVisible.value = false;
    resetForm();
  }
};

// 获取二维码图片源
const getQrcodeImageSrc = () => {
  if (!qrcodeData.value) return '';

  // 抖音和小红书需要添加base64前缀
  if (selectedPlatform.value === 1 || selectedPlatform.value === 4) {
    return `data:image/png;base64,${qrcodeData.value.qrcode}`;
  }

  // 快手和视频号直接使用URL
  return qrcodeData.value.qrcode;
};

// 监听弹窗关闭，重置表单
watch(modalVisible, (newVal) => {
  if (!newVal) {
    resetForm();
    clearTimers();
  }
});
</script>

<template>
  <AModal
    v-model:visible="modalVisible"
    title="添加账户"
    width="600px"
    centered
    :footer="null"
    :mask-closable="false"
    @cancel="handleCancel"
  >
    <!-- 步骤指示器 -->
    <ASteps :current="currentStep" class="add-account-steps">
      <AStep title="选择平台" />
      <AStep title="填写信息" />
      <AStep
        :title="
          selectedPlatform === 4 && formData.bindingType === 'cookie'
            ? 'Cookie绑定'
            : '扫码授权'
        "
      />
    </ASteps>

    <div class="step-content">
      <!-- 第一步：平台选择 -->
      <div v-if="currentStep === 0" class="platform-selection">
        <h3 class="step-title">请选择要添加账户的平台</h3>
        <div class="platform-grid">
          <div
            v-for="platform in platformOptions"
            :key="platform.value"
            class="platform-option"
            @click="handlePlatformSelect(platform.value)"
          >
            <div class="platform-icon">{{ platform.icon }}</div>
            <div class="platform-name">{{ platform.label }}</div>
          </div>
        </div>
      </div>

      <!-- 第二步：表单填写 -->
      <div v-if="currentStep === 1" class="form-section">
        <h3 class="step-title">
          {{ selectedPlatformInfo?.icon }} {{ selectedPlatformInfo?.label }} -
          账户信息
        </h3>

        <AForm :model="formData" :rules="formRules" layout="vertical">
          <!-- 分组选择（所有平台都需要） -->
          <AFormItem label="选择分组" name="groupId" required>
            <ARow :gutter="8">
              <ACol :span="20">
                <ASelect
                  v-model:value="formData.groupId"
                  placeholder="请选择分组"
                  :loading="groupLoading"
                >
                  <ASelectOption
                    v-for="group in groupOptions"
                    :key="group.value"
                    :value="group.value"
                  >
                    {{ group.label }}
                  </ASelectOption>
                </ASelect>
              </ACol>
              <ACol :span="4">
                <AButton @click="handleAddGroup" block> 新增分组 </AButton>
              </ACol>
            </ARow>
          </AFormItem>

          <!-- 城市选择（仅抖音需要） -->
          <AFormItem
            v-if="selectedPlatform === 1"
            label="选择城市"
            name="city"
            required
          >
            <ACascader
              v-model:value="formData.city"
              :options="cityOptions"
              placeholder="请选择城市"
              :loading="cityLoading"
            />
          </AFormItem>

          <!-- 账户名称（仅视频号需要） -->
          <AFormItem
            v-if="selectedPlatform === 3"
            label="账户名称"
            name="accountName"
            required
          >
            <AInput
              v-model:value="formData.accountName"
              placeholder="请输入账户名称"
              :maxlength="50"
              show-count
            />
          </AFormItem>

          <!-- 绑定类型（仅小红书需要） -->
          <AFormItem
            v-if="selectedPlatform === 4"
            label="绑定类型"
            name="bindingType"
            required
          >
            <ARadioGroup v-model:value="formData.bindingType">
              <ARadio value="qrcode">扫码绑定</ARadio>
              <ARadio value="cookie">Cookie绑定</ARadio>
            </ARadioGroup>
          </AFormItem>
        </AForm>
      </div>

      <!-- 第三步：二维码扫描 -->
      <div v-if="currentStep === 2" class="qrcode-section">
        <h3 class="step-title">
          {{ selectedPlatformInfo?.icon }} {{ selectedPlatformInfo?.label }} -
          <span
            v-if="selectedPlatform === 4 && formData.bindingType === 'cookie'"
          >
            Cookie绑定
          </span>
          <span v-else> 扫码授权 </span>
        </h3>

        <!-- 小红书Cookie绑定 -->
        <div
          v-if="selectedPlatform === 4 && formData.bindingType === 'cookie'"
          class="cookie-input-section"
        >
          <AForm layout="vertical">
            <AFormItem label="请输入Cookie" required>
              <AInput.TextArea
                v-model:value="cookieInput"
                placeholder="请输入完整的Cookie信息，Cookie长度通常较长，请确保完整复制"
                :rows="8"
                :maxlength="10000"
                show-count
              />
            </AFormItem>
          </AForm>
        </div>

        <!-- 二维码扫描 -->
        <div v-else class="qrcode-container">
          <div class="qrcode-wrapper" :class="{ expired: qrcodeExpired }">
            <div v-if="qrcodeLoading" class="qrcode-loading">
              <ASpin size="large" />
              <p>正在生成二维码...</p>
            </div>

            <div v-else-if="qrcodeData" class="qrcode-content">
              <!-- 二维码图片 -->
              <div class="qrcode-image">
                <img
                  :src="getQrcodeImageSrc()"
                  alt="二维码"
                  class="qrcode-img"
                />

                <!-- 过期遮罩 -->
                <div v-if="qrcodeExpired" class="qrcode-overlay">
                  <div class="overlay-content">
                    <p>二维码已过期</p>
                    <AButton type="primary" @click="refreshQrcode">
                      点击刷新
                    </AButton>
                  </div>
                </div>

                <!-- 扫描成功遮罩 -->
                <div v-if="scanSuccess" class="qrcode-overlay">
                  <div class="overlay-content">
                    <p v-if="selectedPlatform === 1">✅ 扫码成功</p>
                    <p v-else>✅ 扫描成功</p>
                  </div>
                </div>
              </div>

              <!-- 倒计时 -->
              <div
                v-if="
                  (selectedPlatform === 1 || selectedPlatform === 3) &&
                  countdown > 0 &&
                  !scanSuccess
                "
                class="countdown"
              >
                {{ countdown }}秒后过期
              </div>

              <!-- 抖音验证码输入 -->
              <div
                v-if="selectedPlatform === 1 && scanSuccess"
                class="verification-section"
              >
                <AFormItem label="请输入验证码">
                  <AInput
                    v-model:value="verificationCode"
                    placeholder="请输入验证码"
                    :maxlength="6"
                  />
                </AFormItem>
              </div>
            </div>
          </div>

          <!-- 扫描提示 -->
          <div class="scan-tips">
            <p v-if="selectedPlatform === 1">请使用抖音APP扫描二维码</p>
            <p v-else-if="selectedPlatform === 2">请使用快手APP扫描二维码</p>
            <p v-else-if="selectedPlatform === 3">请使用微信扫描二维码</p>
            <p v-else-if="selectedPlatform === 4">请使用小红书APP扫描二维码</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="modal-footer">
      <AButton
        v-if="currentStep === 1 || currentStep === 2"
        @click="handleBack"
      >
        返回
      </AButton>
      <AButton @click="handleCancel"> 取消 </AButton>
      <AButton
        v-if="currentStep === 1"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        确认
      </AButton>
      <AButton
        v-if="
          currentStep === 2 &&
          selectedPlatform === 4 &&
          formData.bindingType === 'cookie'
        "
        type="primary"
        :loading="loading"
        @click="handleCookieSubmit"
      >
        提交Cookie
      </AButton>
      <AButton
        v-if="currentStep === 2 && selectedPlatform === 1 && scanSuccess"
        type="primary"
        :loading="loading"
        @click="handleCompleteAuth"
      >
        完成授权
      </AButton>
    </div>

    <!-- 新增分组弹窗 -->
    <AModal
      v-model:visible="addGroupModalVisible"
      title="新增分组"
      width="400px"
      :confirm-loading="addGroupLoading"
      @ok="handleAddGroupSubmit"
      @cancel="handleAddGroupCancel"
    >
      <AForm layout="vertical">
        <AFormItem label="分组名称" required>
          <AInput
            v-model:value="newGroupName"
            placeholder="请输入分组名称"
            :maxlength="20"
            show-count
          />
        </AFormItem>
      </AForm>
    </AModal>
  </AModal>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .platform-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .platform-option {
    padding: 16px;
  }

  .platform-icon {
    font-size: 28px;
  }

  .form-section {
    padding: 0 10px;
  }

  .qrcode-section {
    padding: 0 10px;
  }

  .qrcode-img {
    width: 160px;
    height: 160px;
  }

  .modal-footer {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .modal-footer .ant-btn {
    width: 100%;
  }
}

.add-account-steps {
  margin-bottom: 24px;
}

.step-content {
  min-height: 300px;
  padding: 20px 0;
}

.step-title {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.platform-selection {
  text-align: center;
}

.platform-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 20px;
}

.platform-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.platform-option:hover {
  background-color: #f6ffed;
  border-color: #1890ff;
}

.platform-icon {
  margin-bottom: 8px;
  font-size: 32px;
}

.platform-name {
  font-size: 14px;
  font-weight: 500;
}

.form-section {
  padding: 0 20px;
}

.modal-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 16px;
  margin-top: 24px;
  border-top: 1px solid #f0f0f0;
}

/* 二维码相关样式 */
.qrcode-section {
  padding: 0 20px;
  text-align: center;
}

.cookie-input-section {
  max-width: 500px;
  margin: 0 auto;
  text-align: left;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.qrcode-wrapper {
  position: relative;
  display: inline-block;
}

.qrcode-wrapper.expired {
  opacity: 0.6;
}

.qrcode-loading {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  padding: 40px;
}

.qrcode-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.qrcode-image {
  position: relative;
  display: inline-block;
}

.qrcode-img {
  width: 200px;
  height: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.qrcode-overlay {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 70%);
  border-radius: 8px;
}

.overlay-content {
  color: white;
  text-align: center;
}

.overlay-content p {
  margin-bottom: 12px;
  font-size: 16px;
}

.countdown {
  font-size: 14px;
  font-weight: 500;
  color: #ff4d4f;
}

.verification-section {
  width: 200px;
}

.scan-tips {
  font-size: 14px;
  color: #666;
}
</style>
